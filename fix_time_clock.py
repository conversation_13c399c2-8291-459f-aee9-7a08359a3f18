#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Script to fix time.clock() calls to time.perf_counter()
"""

import re
import sys

def fix_time_clock(file_path):
    """Fix time.clock() calls in a file"""
    print(f"Fixing time.clock() calls in {file_path}")
    
    with open(file_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # Replace time.clock() with time.perf_counter()
    new_content = content.replace('time.clock()', 'time.perf_counter()')
    
    changes_made = content.count('time.clock()')
    
    if changes_made > 0:
        # Write back to file
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(new_content)
        
        print(f"  Fixed {changes_made} time.clock() calls in {file_path}")
        return True
    else:
        print(f"  No time.clock() calls to fix in {file_path}")
        return False

def main():
    """Main function"""
    files_to_fix = [
        'abeles/test_derivatives.py'
    ]
    
    total_files_modified = 0
    
    for file_path in files_to_fix:
        try:
            if fix_time_clock(file_path):
                total_files_modified += 1
        except Exception as e:
            print(f"Error fixing {file_path}: {e}")
    
    print(f"\nSummary: Modified {total_files_modified} files")

if __name__ == "__main__":
    main()
