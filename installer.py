# installer.py
# 
# Generate a NSIS script to create an installer for OpenFilters. It
# is inspired by by example2.nsi and http://nsis.sourceforge.net/
# Auto-uninstall_old_before_installing_new.
# 
# Copyright (c) 2006,2007,2009,2010,2012-2015 <PERSON><PERSON>.
# 
# This file is part of OpenFilters.
# 
# OpenFilters is free software; you can redistribute it and/or modify
# it under the terms of the GNU General Public License as published by
# the Free Software Foundation; either version 2 of the License, or (at
# your option) any later version.
#
# OpenFilters is distributed in the hope that it will be useful, but
# WITHOUT ANY WARRANTY; without even the implied warranty of
# MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
# General Public License for more details.
#
# You should have received a copy of the GNU General Public License
# along with this program; if not, write to the Free Software
# Foundation, Inc., 59 Temple Place, Suite 330, Boston, MA 02111-1307
# USA


import os, os.path
import platform

import release


# Determine if we are running a 32 bit or a 64 bit version of Python.
bits, _ = platform.architecture()


# Gather all the directories and seperate the sources and examples.

base_directory = "dist"

directories = ["."]

example_directories = []
example_files = []
source_directories = []
source_files = []
executable_directories = []
executable_files = []

while len(directories) > 0:
	directory = directories.pop()
	
	# List subdirectories
	subdirectories = [os.path.join(directory, filename) for filename in os.listdir(os.path.join(base_directory, directory)) if os.path.isdir(os.path.join(base_directory, directory, filename))]
	subdirectories.reverse()
	directories += subdirectories
	
	# Seperate the examples, the sources and other directories.
	if "sources" in directory:
		source_directories.append(directory)
		source_files.append([os.path.join(directory, filename) for filename in os.listdir(os.path.join(base_directory, directory)) if os.path.isfile(os.path.join(base_directory, directory, filename))])
	elif "examples" in directory:
		example_directories.append(directory)
		example_files.append([os.path.join(directory, filename) for filename in os.listdir(os.path.join(base_directory, directory)) if os.path.isfile(os.path.join(base_directory, directory, filename))])
	else:
		executable_directories.append(directory)
		executable_files.append([os.path.join(directory, filename) for filename in os.listdir(os.path.join(base_directory, directory)) if os.path.isfile(os.path.join(base_directory, directory, filename))])


# Write the script.

script = open("OpenFilters.nsi", "w")

# Write a header.
script.write("; DO NOT EDIT THIS FILE.\n")
script.write("; \n")
script.write("; This script was automatically generated by installer.py.\n")
script.write("\n")

# Include the VersionCompare macro from the WordFunc header.
script.write("!include \"WordFunc.nsh\"\n")
script.write("!insertmacro VersionCompare\n")

# Set the compression: lzma compression is longer, but more effective.
script.write("SetCompressor lzma\n")
script.write("\n")

# Set the version number, the number of bits, and the full name.
script.write("!define VERSION %s\n" % release.VERSION)
script.write("!define BITS %s\n" % bits)
script.write("!define NAME \"OpenFilters ${VERSION}\"\n")
script.write("\n")

# The name of the software.
script.write("Name \"${NAME}\"\n")
script.write("\n")

# The file to write.
script.write("OutFile \"Install_OpenFilters_${VERSION}_${BITS}.exe\"\n")
script.write("\n")

# The default installation directory according to the architecture.
if bits == 32:
	script.write("InstallDir $PROGRAMFILES\OpenFilters\n")
else:
	script.write("InstallDir $PROGRAMFILES64\OpenFilters\n")
script.write("\n")

# Registry key to check for directory (so if you install again, it will 
# overwrite the old one automatically).
script.write("InstallDirRegKey HKLM \"Software\OpenFilters\" \"Install_Dir\"\n")
script.write("\n")


# Upon initialization, we verify if OpenFilters is already installed
# and show a message dependant on the version that is already
# installed.
script.write("Function .onInit\n")

script.write("\tVar /GLOBAL UNINSTALLER\n")
script.write("\tVar /GLOBAL INSTALLED_VERSION\n")
script.write("\tVar /GLOBAL COMPARISON\n")
 
script.write("\tReadRegStr $UNINSTALLER HKLM \"Software\Microsoft\Windows\CurrentVersion\Uninstall\OpenFilters\" \"UninstallString\"\n")
script.write("\tStrCmp $UNINSTALLER \"\" done\n")
script.write("\t\n")

script.write("\tReadRegStr $INSTALLED_VERSION HKLM \"Software\Microsoft\Windows\CurrentVersion\Uninstall\OpenFilters\" \"DisplayVersion\"\n")
script.write("\tStrCmp $INSTALLED_VERSION \"\" older_version\n")
script.write("\t${VersionCompare} $INSTALLED_VERSION ${VERSION} $COMPARISON\n")
script.write("\tStrCmp $COMPARISON \"0\" same_version\n")
script.write("\tStrCmp $COMPARISON \"1\" newer_version\n")
script.write("\tStrCmp $COMPARISON \"2\" older_version\n")
script.write("\t\n")

script.write("\tolder_version:\n")
script.write("\tMessageBox MB_YESNO|MB_ICONEXCLAMATION \\\n")
script.write("\t\"An older version of OpenFilters is already installed and needs to be uninstalled before this installation can proceed. Do you wish to uninstall the older version?\" \\\n")
script.write("\tIDYES uninstall\n")
script.write("\tAbort\n")
script.write("\t\n")

script.write("\tsame_version:\n")
script.write("\tMessageBox MB_YESNO|MB_ICONEXCLAMATION \\\n")
script.write("\t\"This version of OpenFilters is already installed. Do you wish to reinstall it?\" \\\n")
script.write("\tIDYES uninstall\n")
script.write("\tAbort\n")
script.write("\t\n")

script.write("\tnewer_version:\n")
script.write("\tMessageBox MB_YESNO|MB_ICONEXCLAMATION \\\n")
script.write("\t\"A newer version of OpenFilters is already installed. Do you wish to uninstall the newer version and downgrade to this version?\" \\\n")
script.write("\tIDYES uninstall\n")
script.write("\tAbort\n")
script.write("\t\n")

script.write("\tuninstall:\n")
script.write("\tExec \"$UNINSTALLER /S\"\n")
script.write("\t\n")

script.write("\tdone:\n")
script.write("\t\n") 

script.write("FunctionEnd\n")


script.write("\t\n") 

# Pages.
script.write("Page components\n")
script.write("Page directory\n")
script.write("Page instfiles\n")
script.write("\n")
script.write("UninstPage uninstConfirm\n")
script.write("UninstPage instfiles\n")
script.write("\n")

# The OpenFilters section is required.
script.write("Section \"OpenFilters (required)\"\n")
script.write("\t\n")
script.write("\tSectionIn RO\n")
script.write("\t\n")

# Put all the files in the appropriate directories.
for i in range(len(executable_directories)):
	directory = executable_directories[i]
	if directory == "":
		script.write("\tSetOutPath \"$INSTDIR\"\n")
	else:
		script.write("\tSetOutPath \"$INSTDIR\\%s\"\n" % directory)
	for file in executable_files[i]:
		script.write("\tFile \"%s\"\n" % os.path.join(base_directory, file))
	script.write("\t\n")

# Write the installation path into the registry.
script.write("\tWriteRegStr HKLM SOFTWARE\OpenFilters \"Install_Dir\" \"$INSTDIR\"\n")  
script.write("\t\n")

# Write the uninstall keys for Windows.
script.write("\tWriteRegStr HKLM \"Software\Microsoft\Windows\CurrentVersion\Uninstall\OpenFilters\" \"DisplayName\" \"OpenFilters\"\n")
script.write("\tWriteRegStr HKLM \"Software\Microsoft\Windows\CurrentVersion\Uninstall\OpenFilters\" \"DisplayVersion\" \"${VERSION}\"\n")
script.write("\tWriteRegStr HKLM \"Software\Microsoft\Windows\CurrentVersion\Uninstall\OpenFilters\" \"UninstallString\" \'\"$INSTDIR\uninstall.exe\"\'\n")
script.write("\tWriteRegDWORD HKLM \"Software\Microsoft\Windows\CurrentVersion\Uninstall\OpenFilters\" \"NoModify\" 1\n")
script.write("\tWriteRegDWORD HKLM \"Software\Microsoft\Windows\CurrentVersion\Uninstall\OpenFilters\" \"NoRepair\" 1\n")
script.write("\t\n")
script.write("\tWriteUninstaller \"uninstall.exe\"\n")
script.write("\t\n")

script.write("SectionEnd\n")

script.write("\n")

# The examples section is optional.
script.write("Section \"Examples\"\n")
script.write("\t\n")

# Put all the files in the appropriate directories.
for i in range(len(example_directories)):
	directory = example_directories[i]
	script.write("\tSetOutPath \"$INSTDIR\\%s\"\n" % directory)
	for file in example_files[i]:
		script.write("\tFile \"%s\"\n" % os.path.join(base_directory, file))
	script.write("\t\n")

script.write("SectionEnd\n")

script.write("\n")

# The sources section is optional.
script.write("Section /o \"Source code\"\n")
script.write("\t\n")

# Put all the files in the appropriate directories.
for i in range(len(source_directories)):
	directory = source_directories[i]
	script.write("\tSetOutPath \"$INSTDIR\\%s\"\n" % directory)
	for file in source_files[i]:
		script.write("\tFile \"%s\"\n" % os.path.join(base_directory, file))
	script.write("\t\n")

script.write("SectionEnd\n")

script.write("\n")

# Offer the possibility to put shortcuts in the Start Menu.
script.write("Section \"Start Menu Shortcuts\"\n")
script.write("\t\n")
script.write("\tCreateDirectory \"$SMPROGRAMS\OpenFilters\"\n")
script.write("\tCreateShortCut \"$SMPROGRAMS\OpenFilters\Uninstall.lnk\" \"$INSTDIR\uninstall.exe\" \"\" \"$INSTDIR\uninstall.exe\" 0\n")
script.write("\tCreateShortCut \"$SMPROGRAMS\OpenFilters\OpenFilters.lnk\" \"$INSTDIR\OpenFilters.exe\" \"\" \"$INSTDIR\OpenFilters.exe\" 0\n")
script.write("\tCreateShortCut \"$SMPROGRAMS\OpenFilters\Release notes.lnk\" \"$INSTDIR\Release notes.txt\" \"\" \"$INSTDIR\Release notes.txt\" 0\n")
script.write("\tCreateShortCut \"$SMPROGRAMS\OpenFilters\gpl.lnk\" \"$INSTDIR\gpl.txt\" \"\" \"$INSTDIR\gpl.txt\" 0\n")
script.write("\t\n")
script.write("SectionEnd\n")

script.write("\n")

# Uninstaller.
script.write("Section \"Uninstall\"\n")
script.write("\t\n")

# Remove all source files.
for i in range(len(source_directories)-1, -1, -1):
	directory = source_directories[i]
	for file in source_files[i]:
		script.write("\tDelete \"$INSTDIR\%s\"\n" % file)
	script.write("\t\n")
	script.write("\tRMDir \"$INSTDIR\%s\"\n" % directory)
	script.write("\t\n")

# Remove all example files.
for i in range(len(example_directories)-1, -1, -1):
	directory = example_directories[i]
	for file in example_files[i]:
		script.write("\tDelete \"$INSTDIR\%s\"\n" % file)
	script.write("\t\n")
	script.write("\tRMDir \"$INSTDIR\%s\"\n" % directory)
	script.write("\t\n")

# Remove all executable files.
for i in range(len(executable_directories)-1, -1, -1):
	directory = executable_directories[i]
	for file in executable_files[i]:
		script.write("\tDelete \"$INSTDIR\%s\"\n" % file)
	script.write("\t\n")
	if directory == "":
		script.write("\tSetOutPath \"$INSTDIR\"\n")
	else:
		script.write("\tRMDir \"$INSTDIR\%s\"\n" % directory)
	script.write("\t\n")

# Remove registry keys.
script.write("\tDeleteRegKey HKLM \"Software\Microsoft\Windows\CurrentVersion\Uninstall\OpenFilters\"\n")
script.write("\tDeleteRegKey HKLM SOFTWARE\OpenFilters\n")
script.write("\t\n")

# Remove the uninstaller.
script.write("\tDelete $INSTDIR\uninstall.exe\n")
script.write("\t\n")

# Remove shortcuts, if any.
script.write("\tDelete \"$SMPROGRAMS\OpenFilters\Uninstall.lnk\"\n")
script.write("\tDelete \"$SMPROGRAMS\OpenFilters\OpenFilters.lnk\"\n")
script.write("\tDelete \"$SMPROGRAMS\OpenFilters\Release notes.lnk\"\n")
script.write("\tDelete \"$SMPROGRAMS\OpenFilters\gpl.lnk\"\n")
script.write("\t\n")

# Remove directories used
script.write("\tRMDir \"$SMPROGRAMS\OpenFilters\"\n")
script.write("\tRMDir \"$INSTDIR\"\n")
script.write("\t\n")

script.write("SectionEnd\n")
