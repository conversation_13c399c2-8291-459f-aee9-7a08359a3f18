#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
<PERSON><PERSON>t to fix Python 2 print statements to Python 3 print() function calls
"""

import re
import sys

def fix_print_statements(file_path):
    """Fix print statements in a file"""
    print(f"Fixing print statements in {file_path}")
    
    with open(file_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    original_content = content
    
    # Pattern to match print statements (not function calls)
    # This matches: print "something" or print variable or print "format" % values
    # But not: print("something") which is already correct
    
    # Match print followed by space and not opening parenthesis
    pattern = r'^(\s*)print\s+([^(].*)$'
    
    lines = content.split('\n')
    modified_lines = []
    changes_made = 0
    
    for line_num, line in enumerate(lines, 1):
        match = re.match(pattern, line)
        if match:
            indent = match.group(1)
            print_content = match.group(2)
            
            # Convert to function call
            new_line = f'{indent}print({print_content})'
            modified_lines.append(new_line)
            changes_made += 1
            print(f"  Line {line_num}: {line.strip()} -> {new_line.strip()}")
        else:
            modified_lines.append(line)
    
    if changes_made > 0:
        new_content = '\n'.join(modified_lines)
        
        # Write back to file
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(new_content)
        
        print(f"  Fixed {changes_made} print statements in {file_path}")
        return True
    else:
        print(f"  No print statements to fix in {file_path}")
        return False

def main():
    """Main function"""
    files_to_fix = [
        'abeles/test_derivatives.py',
        'moremath/tests.py'
    ]
    
    total_files_modified = 0
    
    for file_path in files_to_fix:
        try:
            if fix_print_statements(file_path):
                total_files_modified += 1
        except Exception as e:
            print(f"Error fixing {file_path}: {e}")
    
    print(f"\nSummary: Modified {total_files_modified} files")

if __name__ == "__main__":
    main()
