# StRD.py
# 
# NIST Statistical Reference Datasets Project datasets for nonlinear
# regression. Taken from:
#   http://www.itl.nist.gov/div898/strd/nls/nls_main.shtml.
# 
# The Python imprementation was done by <PERSON><PERSON>.
# 
# This file is in the public domain. IT IS PROVIDED "AS IS", WITHOUT
# WARRANTY OF ANY KIND.


import math



tests = ["Misra1a", "Chwirut2", "Chwi<PERSON>t1", "Lanczos3", "Gauss1", "Gauss2", "Dan<PERSON>ood", "Misra1b", "Kirby2", "Hahn1", "<PERSON>", "MGH17", "Lanczos1", "Lanczos2", "Gauss3", "Misra1c", "Misra1d", "Rozman1", "ENSO", "MGH09", "Thurber", "BoxBOD", "Rat42", "MGH10", "<PERSON><PERSON><PERSON>4", "Rat<PERSON>", "<PERSON>5"]



def get_test(name):
	
	if name == "Misra1a":
		
		level = "lower"
		
		def f(b, x):
			nb = len(x)
			y = [b[0]*(1.0-math.exp(-b[1]*x[n])) for n in range(nb)]
			return y
		
		def df(b, x):
			nb = len(x)
			dy = [[1.0-math.exp(-b[1]*x[n]) for n in range(nb)],
			      [b[0]*x[n]*math.exp(-b[1]*x[n]) for n in range(nb)]]
			return dy
		
		X = [77.6E0, 114.9E0, 141.1E0, 190.8E0, 239.9E0, 289.0E0, 332.8E0, 378.4E0, 434.8E0, 477.3E0, 536.8E0, 593.1E0, 689.1E0, 760.0E0]
		Y = [10.07E0, 14.73E0, 17.94E0, 23.93E0, 29.61E0, 35.18E0, 40.02E0, 44.82E0, 50.76E0, 55.05E0, 61.01E0, 66.40E0, 75.47E0, 81.78E0]
		
		par = [2.3894212918E+02, 5.5015643181E-04]
		start = [[500, 0.0001], [250, 0.0005]]
		std_dev = [2.7070075241E+00, 7.2668688436E-06]
		SSR = 1.2455138894E-01
		res_std_dev = 1.0187876330E-01
	
	
	elif name == "Chwirut2":
		
		level = "lower"
		
		def f(b, x):
			nb = len(x)
			y = [math.exp(-b[0]*x[n])/(b[1]+b[2]*x[n]) for n in range(nb)]
			return y
		
		def df(b, x):
			nb = len(x)
			dy = [[-x[n]*math.exp(-b[0]*x[n])/(b[1]+b[2]*x[n]) for n in range(nb)],
			      [-math.exp(-b[0]*x[n])/(b[1]+b[2]*x[n])**2 for n in range(nb)],
			      [-math.exp(-b[0]*x[n])/(b[1]+b[2]*x[n])**2*x[n] for n in range(nb)]]
			return dy
		
		X = [0.500E0, 1.000E0, 1.750E0, 3.750E0, 5.750E0, 0.875E0, 2.250E0, 3.250E0, 5.250E0, 0.750E0, 1.750E0, 2.750E0, 4.750E0, 0.625E0, 1.250E0, 2.250E0, 4.250E0,  .500E0, 3.000E0,  .750E0, 3.000E0, 1.500E0, 6.000E0, 3.000E0, 6.000E0, 1.500E0, 3.000E0,  .500E0, 2.000E0, 4.000E0,  .750E0, 2.000E0, 5.000E0,  .750E0, 2.250E0, 3.750E0, 5.750E0, 3.000E0,  .750E0, 2.500E0, 4.000E0,  .750E0, 2.500E0, 4.000E0,  .750E0, 2.500E0, 4.000E0,  .500E0, 6.000E0, 3.000E0,  .500E0, 2.750E0,  .500E0, 1.750E0]
		Y = [92.9000E0, 57.1000E0, 31.0500E0, 11.5875E0,  8.0250E0, 63.6000E0, 21.4000E0, 14.2500E0,  8.4750E0, 63.8000E0, 26.8000E0, 16.4625E0,  7.1250E0, 67.3000E0, 41.0000E0, 21.1500E0,  8.1750E0, 81.5000E0, 13.1200E0, 59.9000E0, 14.6200E0, 32.9000E0,  5.4400E0, 12.5600E0,  5.4400E0, 32.0000E0, 13.9500E0, 75.8000E0, 20.0000E0, 10.4200E0, 59.5000E0, 21.6700E0,  8.5500E0, 62.0000E0, 20.2000E0,  7.7600E0,  3.7500E0, 11.8100E0, 54.7000E0, 23.7000E0, 11.5500E0, 61.3000E0, 17.7000E0,  8.7400E0, 59.2000E0, 16.3000E0,  8.6200E0, 81.0000E0,  4.8700E0, 14.6200E0, 81.7000E0, 17.1700E0, 81.3000E0, 28.9000E0]
		
		start = [[0.1, 0.01, 0.02], [0.15, 0.008, 0.010]]
		par = [1.6657666537E-01, 5.1653291286E-03, 1.2150007096E-02]
		std_dev = [3.8303286810E-02, 6.6621605126E-04, 1.5304234767E-03]
		SSR = 5.1304802941E+02
		res_std_dev = 3.1717133040E+00
	
	
	elif name == "Chwirut1":
		
		level = "lower"
		
		def f(b, x):
			nb = len(x)
			y = [math.exp(-b[0]*x[n])/(b[1]+b[2]*x[n]) for n in range(nb)]
			return y
		
		def df(b, x):
			nb = len(x)
			dy = [[-x[n]*math.exp(-b[0]*x[n])/(b[1]+b[2]*x[n]) for n in range(nb)],
			      [-math.exp(-b[0]*x[n])/(b[1]+b[2]*x[n])**2 for n in range(nb)],
			      [-math.exp(-b[0]*x[n])/(b[1]+b[2]*x[n])**2*x[n] for n in range(nb)]]
			return dy
		
		X = [0.5000E0, 0.6250E0, 0.7500E0, 0.8750E0, 1.0000E0, 1.2500E0, 1.7500E0, 2.2500E0, 1.7500E0, 2.2500E0, 2.7500E0, 3.2500E0, 3.7500E0, 4.2500E0, 4.7500E0, 5.2500E0, 5.7500E0, 0.5000E0, 0.6250E0, 0.7500E0, 0.8750E0, 1.0000E0, 1.2500E0, 1.7500E0, 2.2500E0, 1.7500E0, 2.2500E0, 2.7500E0, 3.2500E0, 3.7500E0, 4.2500E0, 4.7500E0, 5.2500E0, 5.7500E0, 0.5000E0, 0.6250E0, 0.7500E0, 0.8750E0, 1.0000E0, 1.2500E0, 1.7500E0, 2.2500E0, 1.7500E0, 2.2500E0, 2.7500E0, 3.2500E0, 3.7500E0, 4.2500E0, 4.7500E0, 5.2500E0, 5.7500E0, 0.5000E0, 0.6250E0, 0.7500E0, 0.8750E0, 1.0000E0, 1.2500E0, 1.7500E0, 2.2500E0, 1.7500E0, 2.2500E0, 2.7500E0, 3.2500E0, 3.7500E0, 4.2500E0, 4.7500E0, 5.2500E0, 5.7500E0,  .5000E0,  .7500E0, 1.5000E0, 3.0000E0, 3.0000E0, 3.0000E0, 6.0000E0, .5000E0,  .7500E0, 1.5000E0, 3.0000E0, 3.0000E0, 3.0000E0, 6.0000E0,  .5000E0,  .7500E0, 1.5000E0, 3.0000E0, 3.0000E0, 3.0000E0, 6.0000E0,  .5000E0,  .7500E0, 1.5000E0, 3.0000E0, 6.0000E0, 3.0000E0, 3.0000E0, 6.0000E0,  .5000E0,  .7500E0, 1.0000E0, 1.5000E0, 2.0000E0, 2.0000E0, 2.5000E0, 3.0000E0, 4.0000E0, 5.0000E0, 6.0000E0,  .5000E0,  .7500E0, 1.0000E0, 1.5000E0, 2.0000E0, 2.0000E0, 2.5000E0, 3.0000E0, 4.0000E0, 5.0000E0, 6.0000E0,  .5000E0, .7500E0, 1.0000E0, 1.5000E0, 2.0000E0, 2.0000E0, 2.5000E0, 3.0000E0, 4.0000E0, 5.0000E0, 6.0000E0,  .5000E0,  .6250E0,  .7500E0,  .8750E0, 1.0000E0, 1.2500E0, 2.2500E0, 2.2500E0, 2.7500E0, 3.2500E0, 3.7500E0, 4.2500E0, 4.7500E0, 5.2500E0, 5.7500E0, 3.0000E0, 3.0000E0, 3.0000E0, 3.0000E0, 3.0000E0, 3.0000E0,  .5000E0,  .7500E0, 1.0000E0, 1.5000E0, 2.0000E0, 2.5000E0, 2.0000E0, 2.5000E0, 3.0000E0, 4.0000E0, 5.0000E0, 6.0000E0,  .5000E0,  .7500E0, 1.0000E0, 1.5000E0, 2.0000E0, 2.5000E0, 2.0000E0, 2.5000E0, 3.0000E0, 4.0000E0, 5.0000E0, 6.0000E0,  .5000E0,  .7500E0, 1.0000E0, 1.5000E0, 2.0000E0, 2.5000E0, 2.0000E0, 2.5000E0, 3.0000E0, 4.0000E0, 5.0000E0, 6.0000E0, 3.0000E0,  .5000E0,  .7500E0, 1.5000E0, 3.0000E0, 6.0000E0, 3.0000E0, 6.0000E0, 3.0000E0, 3.0000E0, 3.0000E0, 1.7500E0, 1.7500E0,  .5000E0,  .7500E0, 1.7500E0, 1.7500E0, 2.7500E0, 3.7500E0, 1.7500E0, 1.7500E0,  .5000E0,  .7500E0, 2.7500E0, 3.7500E0, 1.7500E0, 1.7500E0]
		Y = [92.9000E0,  78.7000E0, 64.2000E0, 64.9000E0, 57.1000E0, 43.3000E0, 31.1000E0, 23.6000E0, 31.0500E0, 23.7750E0, 17.7375E0, 13.8000E0, 11.5875E0, 9.4125E0, 7.7250E0, 7.3500E0, 8.0250E0, 90.6000E0, 76.9000E0, 71.6000E0, 63.6000E0, 54.0000E0, 39.2000E0, 29.3000E0, 21.4000E0, 29.1750E0, 22.1250E0, 17.5125E0, 14.2500E0, 9.4500E0, 9.1500E0, 7.9125E0, 8.4750E0, 6.1125E0, 80.0000E0, 79.0000E0, 63.8000E0, 57.2000E0, 53.2000E0, 42.5000E0, 26.8000E0, 20.4000E0, 26.8500E0, 21.0000E0, 16.4625E0, 12.5250E0, 10.5375E0, 8.5875E0, 7.1250E0, 6.1125E0, 5.9625E0, 74.1000E0, 67.3000E0, 60.8000E0, 55.5000E0, 50.3000E0, 41.0000E0, 29.4000E0, 20.4000E0, 29.3625E0, 21.1500E0, 16.7625E0, 13.2000E0, 10.8750E0, 8.1750E0, 7.3500E0, 5.9625E0, 5.6250E0, 81.5000E0, 62.4000E0, 32.5000E0, 12.4100E0, 13.1200E0, 15.5600E0, 5.6300E0, 78.0000E0, 59.9000E0, 33.2000E0, 13.8400E0, 12.7500E0, 14.6200E0, 3.9400E0, 76.8000E0, 61.0000E0, 32.9000E0, 13.8700E0, 11.8100E0, 13.3100E0, 5.4400E0, 78.0000E0, 63.5000E0, 33.8000E0, 12.5600E0, 5.6300E0, 12.7500E0, 13.1200E0, 5.4400E0, 76.8000E0, 60.0000E0, 47.8000E0, 32.0000E0, 22.2000E0, 22.5700E0, 18.8200E0, 13.9500E0, 11.2500E0, 9.0000E0, 6.6700E0, 75.8000E0, 62.0000E0, 48.8000E0, 35.2000E0, 20.0000E0, 20.3200E0, 19.3100E0, 12.7500E0, 10.4200E0, 7.3100E0, 7.4200E0, 70.5000E0, 59.5000E0, 48.5000E0, 35.8000E0, 21.0000E0, 21.6700E0, 21.0000E0, 15.6400E0, 8.1700E0, 8.5500E0, 10.1200E0, 78.0000E0, 66.0000E0, 62.0000E0, 58.0000E0, 47.7000E0, 37.8000E0, 20.2000E0, 21.0700E0, 13.8700E0, 9.6700E0, 7.7600E0, 5.4400E0, 4.8700E0, 4.0100E0, 3.7500E0, 24.1900E0, 25.7600E0, 18.0700E0, 11.8100E0, 12.0700E0, 16.1200E0, 70.8000E0, 54.7000E0, 48.0000E0, 39.8000E0, 29.8000E0, 23.7000E0, 29.6200E0, 23.8100E0, 17.7000E0, 11.5500E0, 12.0700E0, 8.7400E0, 80.7000E0, 61.3000E0, 47.5000E0, 29.0000E0, 24.0000E0, 17.7000E0, 24.5600E0, 18.6700E0, 16.2400E0, 8.7400E0, 7.8700E0, 8.5100E0, 66.7000E0, 59.2000E0, 40.8000E0, 30.7000E0, 25.7000E0, 16.3000E0, 25.9900E0, 16.9500E0, 13.3500E0, 8.6200E0, 7.2000E0, 6.6400E0, 13.6900E0, 81.0000E0, 64.5000E0, 35.5000E0, 13.3100E0, 4.8700E0, 12.9400E0, 5.0600E0, 15.1900E0, 14.6200E0, 15.6400E0, 25.5000E0, 25.9500E0, 81.7000E0, 61.6000E0, 29.8000E0, 29.8100E0, 17.1700E0, 10.3900E0, 28.4000E0, 28.6900E0, 81.3000E0, 60.9000E0, 16.6500E0, 10.0500E0, 28.9000E0, 28.9500E0]
		
		start = [[0.1, 0.01, 0.02], [0.15, 0.008, 0.010]]
		par = [1.9027818370E-01, 6.1314004477E-03, 1.0530908399E-02]
		std_dev = [2.1938557035E-02, 3.4500025051E-04, 7.9281847748E-04]
		SSR = 2.3844771393E+03
		res_std_dev = 3.3616721320E+00
	
	
	elif name == "Lanczos3":
		
		level = "lower"
		
		def f(b, x):
			nb = len(x)
			y = [b[0]*math.exp(-b[1]*x[n]) + b[2]*math.exp(-b[3]*x[n]) + b[4]*math.exp(-b[5]*x[n]) for n in range(nb)]
			return y
		
		def df(b, x):
			nb = len(x)
			dy = [[math.exp(-b[1]*x[n]) for n in range(nb)],
			      [-b[0]*x[n]*math.exp(-b[1]*x[n]) for n in range(nb)],
			      [math.exp(-b[3]*x[n]) for n in range(nb)],
			      [-b[2]*x[n]*math.exp(-b[3]*x[n]) for n in range(nb)],
			      [math.exp(-b[5]*x[n]) for n in range(nb)],
			      [-b[4]*x[n]*math.exp(-b[5]*x[n]) for n in range(nb)]]
			return dy
		
		X = [0.00000E+00, 5.00000E-02, 1.00000E-01, 1.50000E-01, 2.00000E-01, 2.50000E-01, 3.00000E-01, 3.50000E-01, 4.00000E-01, 4.50000E-01, 5.00000E-01, 5.50000E-01, 6.00000E-01, 6.50000E-01, 7.00000E-01, 7.50000E-01, 8.00000E-01, 8.50000E-01, 9.00000E-01, 9.50000E-01, 1.00000E+00, 1.05000E+00, 1.10000E+00, 1.15000E+00]
		Y = [2.5134E+00, 2.0443E+00, 1.6684E+00, 1.3664E+00, 1.1232E+00, 0.9269E+00, 0.7679E+00, 0.6389E+00, 0.5338E+00, 0.4479E+00, 0.3776E+00, 0.3197E+00, 0.2720E+00, 0.2325E+00, 0.1997E+00, 0.1723E+00, 0.1493E+00, 0.1301E+00, 0.1138E+00, 0.1000E+00, 0.0883E+00, 0.0783E+00, 0.0698E+00, 0.0624E+00]
		
		start = [[1.2, 0.3, 5.6, 5.5, 6.5, 7.6], [0.5, 0.7, 3.6, 4.2, 4.0, 6.3]]
		par = [8.6816414977E-02, 9.5498101505E-01, 8.4400777463E-01, 2.9515951832E+00, 1.5825685901E+00, 4.9863565084E+00]
		std_dev = [1.7197908859E-02, 9.7041624475E-02, 4.1488663282E-02, 1.0766312506E-01, 5.8371576281E-02, 3.4436403035E-02]
		SSR = 1.6117193594E-08
		res_std_dev = 2.9923229172E-05
	
	
	elif name == "Gauss1":
		
		level = "lower"
		
		def f(b, x):
			nb = len(x)
			y = [b[0]*math.exp(-b[1]*x[n]) + b[2]*math.exp(-((x[n]-b[3])/b[4])**2) + b[5]*math.exp(-((x[n]-b[6])/b[7])**2) for n in range(nb)]
			return y
		
		def df(b, x):
			nb = len(x)
			dy = [[math.exp(-b[1]*x[n]) for n in range(nb)],
			      [-b[0]*x[n]*math.exp(-b[1]*x[n]) for n in range(nb)],
			      [math.exp(-((x[n]-b[3])/b[4])**2) for n in range(nb)],
			      [b[2]*math.exp(-((x[n]-b[3])/b[4])**2)*2.0*(x[n]-b[3])/b[4]**2 for n in range(nb)],
			      [b[2]*math.exp(-((x[n]-b[3])/b[4])**2)*2.0*(x[n]-b[3])**2/b[4]**3 for n in range(nb)],
			      [math.exp(-((x[n]-b[6])/b[7])**2) for n in range(nb)],
			      [b[5]*math.exp(-((x[n]-b[6])/b[7])**2)*2.0*(x[n]-b[6])/b[7]**2 for n in range(nb)],
			      [b[5]*math.exp(-((x[n]-b[6])/b[7])**2)*2.0*(x[n]-b[6])**2/b[7]**3 for n in range(nb)]]
			return dy
		
		X = [1.000000, 2.000000, 3.000000, 4.000000, 5.000000, 6.000000, 7.000000, 8.000000, 9.000000, 10.000000, 11.00000, 12.00000, 13.00000, 14.00000, 15.00000, 16.00000, 17.00000, 18.00000, 19.00000, 20.00000, 21.00000, 22.00000, 23.00000, 24.00000, 25.00000, 26.00000, 27.00000, 28.00000, 29.00000, 30.00000, 31.00000, 32.00000, 33.00000, 34.00000, 35.00000, 36.00000, 37.00000, 38.00000, 39.00000, 40.00000, 41.00000, 42.00000, 43.00000, 44.00000, 45.00000, 46.00000, 47.00000, 48.00000, 49.00000, 50.00000, 51.00000, 52.00000, 53.00000, 54.00000, 55.00000, 56.00000, 57.00000, 58.00000, 59.00000, 60.00000, 61.00000, 62.00000, 63.00000, 64.00000, 65.00000, 66.00000, 67.00000, 68.00000, 69.00000, 70.00000, 71.00000, 72.00000, 73.00000, 74.00000, 75.00000, 76.00000, 77.00000, 78.00000, 79.00000, 80.00000, 81.00000, 82.00000, 83.00000, 84.00000, 85.00000, 86.00000, 87.00000, 88.00000, 89.00000, 90.00000, 91.00000, 92.00000, 93.00000, 94.00000, 95.00000, 96.00000, 97.00000, 98.00000, 99.00000, 100.00000, 101.00000, 102.00000, 103.00000, 104.00000, 105.00000, 106.0000, 107.0000, 108.0000, 109.0000, 110.0000, 111.0000, 112.0000, 113.0000, 114.0000, 115.0000, 116.0000, 117.0000, 118.0000, 119.0000, 120.0000, 121.0000, 122.0000, 123.0000, 124.0000, 125.0000, 126.0000, 127.0000, 128.0000, 129.0000, 130.0000, 131.0000, 132.0000, 133.0000, 134.0000, 135.0000, 136.0000, 137.0000, 138.0000, 139.0000, 140.0000, 141.0000, 142.0000, 143.0000, 144.0000, 145.0000, 146.0000, 147.0000, 148.0000, 149.0000, 150.0000, 151.0000, 152.0000, 153.0000, 154.0000, 155.0000, 156.0000, 157.0000, 158.0000, 159.0000, 160.0000, 161.0000, 162.0000, 163.0000, 164.0000, 165.0000, 166.0000, 167.0000, 168.0000, 169.0000, 170.0000, 171.0000, 172.0000, 173.0000, 174.0000, 175.0000, 176.0000, 177.0000, 178.0000, 179.0000, 180.0000, 181.0000, 182.0000, 183.0000, 184.0000, 185.0000, 186.0000, 187.0000, 188.0000, 189.0000, 190.0000, 191.0000, 192.0000, 193.0000, 194.0000, 195.0000, 196.0000, 197.0000, 198.0000, 199.0000, 200.0000, 201.0000, 202.0000, 203.0000, 204.0000, 205.0000, 206.0000, 207.0000, 208.0000, 209.0000, 210.0000, 211.0000, 212.0000, 213.0000, 214.0000, 215.0000, 216.0000, 217.0000, 218.0000, 219.0000, 220.0000, 221.0000, 222.0000, 223.0000, 224.0000, 225.0000, 226.0000, 227.0000, 228.0000, 229.0000, 230.0000, 231.0000, 232.0000, 233.0000, 234.0000, 235.0000, 236.0000, 237.0000, 238.0000, 239.0000, 240.0000, 241.0000, 242.0000, 243.0000, 244.0000, 245.0000, 246.0000, 247.0000, 248.0000, 249.0000, 250.0000]
		Y = [97.62227, 97.80724, 96.62247, 92.59022, 91.23869, 95.32704, 90.35040, 89.46235, 91.72520, 89.86916, 86.88076, 85.94360, 87.60686, 86.25839, 80.74976, 83.03551, 88.25837, 82.01316, 82.74098, 83.30034, 81.27850, 81.85506, 80.75195, 80.09573, 81.07633, 78.81542, 78.38596, 79.93386, 79.48474, 79.95942, 76.10691, 78.39830, 81.43060, 82.48867, 81.65462, 80.84323, 88.68663, 84.74438, 86.83934, 85.97739, 91.28509, 97.22411, 93.51733, 94.10159, 101.91760, 98.43134, 110.4214, 107.6628, 111.7288, 116.5115, 120.7609, 123.9553, 124.2437, 130.7996, 133.2960, 130.7788, 132.0565, 138.6584, 142.9252, 142.7215, 144.1249, 147.4377, 148.2647, 152.0519, 147.3863, 149.2074, 148.9537, 144.5876, 148.1226, 148.0144, 143.8893, 140.9088, 143.4434, 139.3938, 135.9878, 136.3927, 126.7262, 124.4487, 122.8647, 113.8557, 113.7037, 106.8407, 107.0034, 102.46290, 96.09296, 94.57555, 86.98824, 84.90154, 81.18023, 76.40117, 67.09200, 72.67155, 68.10848, 67.99088, 63.34094, 60.55253, 56.18687, 53.64482, 53.70307, 48.07893, 42.21258, 45.65181, 41.69728, 41.24946, 39.21349, 37.71696, 36.68395, 37.30393, 37.43277, 37.45012, 32.64648, 31.84347, 31.39951, 26.68912, 32.25323, 27.61008, 33.58649, 28.10714, 30.26428, 28.01648, 29.11021, 23.02099, 25.65091, 28.50295, 25.23701, 26.13828, 33.53260, 29.25195, 27.09847, 26.52999, 25.52401, 26.69218, 24.55269, 27.71763, 25.20297, 25.61483, 25.06893, 27.63930, 24.94851, 25.86806, 22.48183, 26.90045, 25.39919, 17.90614, 23.76039, 25.89689, 27.64231, 22.86101, 26.47003, 23.72888, 27.54334, 30.52683, 28.07261, 34.92815, 28.29194, 34.19161, 35.41207, 37.09336, 40.98330, 39.53923, 47.80123, 47.46305, 51.04166, 54.58065, 57.53001, 61.42089, 62.79032, 68.51455, 70.23053, 74.42776, 76.59911, 81.62053, 83.42208, 79.17451, 88.56985, 85.66525, 86.55502, 90.65907, 84.27290, 85.72220, 83.10702, 82.16884, 80.42568, 78.15692, 79.79691, 77.84378, 74.50327, 71.57289, 65.88031, 65.01385, 60.19582, 59.66726, 52.95478, 53.87792, 44.91274, 41.09909, 41.68018, 34.53379, 34.86419, 33.14787, 29.58864, 27.29462, 21.91439, 19.08159, 24.90290, 19.82341, 16.75551, 18.24558, 17.23549, 16.34934, 13.71285, 14.75676, 13.97169, 12.42867, 14.35519, 7.703309, 10.234410, 11.78315, 13.87768, 4.535700, 10.059280, 8.424824, 10.533120, 9.602255, 7.877514, 6.258121, 8.899865, 7.877754, 12.51191, 10.66205, 6.035400, 6.790655, 8.783535, 4.600288, 8.400915, 7.216561, 10.017410, 7.331278, 6.527863, 2.842001, 10.325070, 4.790995, 8.377101, 6.264445, 2.706213, 8.362329, 8.983658, 3.362571, 1.182746, 4.875359]
		
		start = [[97.0, 0.009, 100.0, 65.0, 20.0, 70.0, 178.0, 16.5], [94.0, 0.0105, 99.0, 63.0, 25.0, 71.0, 180.0, 20.0]]
		par = [9.8778210871E+01, 1.0497276517E-02, 1.0048990633E+02, 6.7481111276E+01, 2.3129773360E+01, 7.1994503004E+01, 1.7899805021E+02, 1.8389389025E+01]
		std_dev = [5.7527312730E-01, 1.1406289017E-04, 5.8831775752E-01, 1.0460593412E-01, 1.7439951146E-01, 6.2622793913E-01, 1.2436988217E-01, 2.0134312832E-01]
		SSR = 1.3158222432E+03
		res_std_dev = 2.3317980180E+00
	
	
	elif name == "Gauss2":
		
		level = "lower"
		
		def f(b, x):
			nb = len(x)
			y = [b[0]*math.exp(-b[1]*x[n]) + b[2]*math.exp(-((x[n]-b[3])/b[4])**2) + b[5]*math.exp(-((x[n]-b[6])/b[7])**2) for n in range(nb)]
			return y
		
		def df(b, x):
			nb = len(x)
			dy = [[math.exp(-b[1]*x[n]) for n in range(nb)],
			      [-b[0]*x[n]*math.exp(-b[1]*x[n]) for n in range(nb)],
			      [math.exp(-((x[n]-b[3])/b[4])**2) for n in range(nb)],
			      [b[2]*math.exp(-((x[n]-b[3])/b[4])**2)*2.0*(x[n]-b[3])/b[4]**2 for n in range(nb)],
			      [b[2]*math.exp(-((x[n]-b[3])/b[4])**2)*2.0*(x[n]-b[3])**2/b[4]**3 for n in range(nb)],
			      [math.exp(-((x[n]-b[6])/b[7])**2) for n in range(nb)],
			      [b[5]*math.exp(-((x[n]-b[6])/b[7])**2)*2.0*(x[n]-b[6])/b[7]**2 for n in range(nb)],
			      [b[5]*math.exp(-((x[n]-b[6])/b[7])**2)*2.0*(x[n]-b[6])**2/b[7]**3 for n in range(nb)]]
			return dy
		
		X = [1.000000, 2.000000, 3.000000, 4.000000, 5.000000, 6.000000, 7.000000, 8.000000, 9.000000, 10.000000, 11.00000, 12.00000, 13.00000, 14.00000, 15.00000, 16.00000, 17.00000, 18.00000, 19.00000, 20.00000, 21.00000, 22.00000, 23.00000, 24.00000, 25.00000, 26.00000, 27.00000, 28.00000, 29.00000, 30.00000, 31.00000, 32.00000, 33.00000, 34.00000, 35.00000, 36.00000, 37.00000, 38.00000, 39.00000, 40.00000, 41.00000, 42.00000, 43.00000, 44.00000, 45.00000, 46.00000, 47.00000, 48.00000, 49.00000, 50.00000, 51.00000, 52.00000, 53.00000, 54.00000, 55.00000, 56.00000, 57.00000, 58.00000, 59.00000, 60.00000, 61.00000, 62.00000, 63.00000, 64.00000, 65.00000, 66.00000, 67.00000, 68.00000, 69.00000, 70.00000, 71.00000, 72.00000, 73.00000, 74.00000, 75.00000, 76.00000, 77.00000, 78.00000, 79.00000, 80.00000, 81.00000, 82.00000, 83.00000, 84.00000, 85.00000, 86.00000, 87.00000, 88.00000, 89.00000, 90.00000, 91.00000, 92.00000, 93.00000, 94.00000, 95.00000, 96.00000, 97.00000, 98.00000, 99.00000, 100.00000, 101.00000, 102.00000, 103.00000, 104.00000, 105.00000, 106.0000, 107.0000, 108.0000, 109.0000, 110.0000, 111.0000, 112.0000, 113.0000, 114.0000, 115.0000, 116.0000, 117.0000, 118.0000, 119.0000, 120.0000, 121.0000, 122.0000, 123.0000, 124.0000, 125.0000, 126.0000, 127.0000, 128.0000, 129.0000, 130.0000, 131.0000, 132.0000, 133.0000, 134.0000, 135.0000, 136.0000, 137.0000, 138.0000, 139.0000, 140.0000, 141.0000, 142.0000, 143.0000, 144.0000, 145.0000, 146.0000, 147.0000, 148.0000, 149.0000, 150.0000, 151.0000, 152.0000, 153.0000, 154.0000, 155.0000, 156.0000, 157.0000, 158.0000, 159.0000, 160.0000, 161.0000, 162.0000, 163.0000, 164.0000, 165.0000, 166.0000, 167.0000, 168.0000, 169.0000, 170.0000, 171.0000, 172.0000, 173.0000, 174.0000, 175.0000, 176.0000, 177.0000, 178.0000, 179.0000, 180.0000, 181.0000, 182.0000, 183.0000, 184.0000, 185.0000, 186.0000, 187.0000, 188.0000, 189.0000, 190.0000, 191.0000, 192.0000, 193.0000, 194.0000, 195.0000, 196.0000, 197.0000, 198.0000, 199.0000, 200.0000, 201.0000, 202.0000, 203.0000, 204.0000, 205.0000, 206.0000, 207.0000, 208.0000, 209.0000, 210.0000, 211.0000, 212.0000, 213.0000, 214.0000, 215.0000, 216.0000, 217.0000, 218.0000, 219.0000, 220.0000, 221.0000, 222.0000, 223.0000, 224.0000, 225.0000, 226.0000, 227.0000, 228.0000, 229.0000, 230.0000, 231.0000, 232.0000, 233.0000, 234.0000, 235.0000, 236.0000, 237.0000, 238.0000, 239.0000, 240.0000, 241.0000, 242.0000, 243.0000, 244.0000, 245.0000, 246.0000, 247.0000, 248.0000, 249.0000, 250.0000]
		Y = [97.58776, 97.76344, 96.56705, 92.52037, 91.15097, 95.21728, 90.21355, 89.29235, 91.51479, 89.60966, 86.56187, 85.55316, 87.13054, 85.67940, 80.04851, 82.18925, 87.24081, 80.79407, 81.28570, 81.56940, 79.22715, 79.43275, 77.90195, 76.75468, 77.17377, 74.27348, 73.11900, 73.84826, 72.47870, 71.92292, 66.92176, 67.93835, 69.56207, 69.07066, 66.53983, 63.87883, 69.71537, 63.60588, 63.37154, 60.01835, 62.67481, 65.80666, 59.14304, 56.62951, 61.21785, 54.38790, 62.93443, 56.65144, 57.13362, 58.29689, 58.91744, 58.50172, 55.22885, 58.30375, 57.43237, 51.69407, 49.93132, 53.70760, 55.39712, 52.89709, 52.31649, 53.98720, 53.54158, 56.45046, 51.32276, 53.11676, 53.28631, 49.80555, 54.69564, 56.41627, 54.59362, 54.38520, 60.15354, 59.78773, 60.49995, 65.43885, 60.70001, 63.71865, 67.77139, 64.70934, 70.78193, 70.38651, 77.22359, 79.52665, 80.13077, 85.67823, 85.20647, 90.24548, 93.61953, 95.86509, 93.46992, 105.8137, 107.8269, 114.0607, 115.5019, 118.5110, 119.6177, 122.1940, 126.9903, 125.7005, 123.7447, 130.6543, 129.7168, 131.8240, 131.8759, 131.9994, 132.1221, 133.4414, 133.8252, 133.6695, 128.2851, 126.5182, 124.7550, 118.4016, 122.0334, 115.2059, 118.7856, 110.7387, 110.2003, 105.17290, 103.44720, 94.54280, 94.40526, 94.57964, 88.76605, 87.28747, 92.50443, 86.27997, 82.44307, 80.47367, 78.36608, 78.74307, 76.12786, 79.13108, 76.76062, 77.60769, 77.76633, 81.28220, 79.74307, 81.97964, 80.02952, 85.95232, 85.96838, 79.94789, 87.17023, 90.50992, 93.23373, 89.14803, 93.11492, 90.34337, 93.69421, 95.74256, 91.85105, 96.74503, 87.60996, 90.47012, 88.11690, 85.70673, 85.01361, 78.53040, 81.34148, 75.19295, 72.66115, 69.85504, 66.29476, 63.58502, 58.33847, 57.50766, 52.80498, 50.79319, 47.03490, 46.47090, 43.09016, 34.11531, 39.28235, 32.68386, 30.44056, 31.98932, 23.63330, 23.69643, 20.26812, 19.07074, 17.59544, 16.08785, 18.94267, 18.61354, 17.25800, 16.62285, 13.48367, 15.37647, 13.47208, 15.96188, 12.32547, 16.33880, 10.438330, 9.628715, 13.12268, 8.772417, 11.76143, 12.55020, 11.33108, 11.20493, 7.816916, 6.800675, 14.26581, 10.66285, 8.911574, 11.56733, 11.58207, 11.59071, 9.730134, 11.44237, 11.22912, 10.172130, 12.50905, 6.201493, 9.019605, 10.80607, 13.09625, 3.914271, 9.567886, 8.038448, 10.231040, 9.367410, 7.695971, 6.118575, 8.793207, 7.796692, 12.45065, 10.61601, 6.001003, 6.765098, 8.764653, 4.586418, 8.390783, 7.209202, 10.012090, 7.327461, 6.525136, 2.840065, 10.323710, 4.790035, 8.376431, 6.263980, 2.705892, 8.362109, 8.983507, 3.362469, 1.182678, 4.875312]
		
		start = [[96.0, 0.009, 103.0, 106.0, 18.0, 72.0, 151.0, 18.0], [98.0, 0.0105, 103.0, 105.0, 20.0, 73.0, 150.0, 20.0]]
		par = [9.9018328406E+01, 1.0994945399E-02, 1.0188022528E+02, 1.0703095519E+02, 2.3578584029E+01, 7.2045589471E+01, 1.5327010194E+02, 1.9525972636E+01]
		std_dev = [5.3748766879E-01, 1.3335306766E-04, 5.9217315772E-01, 1.5006798316E-01, 2.2695595067E-01, 6.1721965884E-01, 1.9466674341E-01, 2.6416549393E-01]
		SSR = 1.2475282092E+03
		res_std_dev = 2.2704790782E+00
	
	
	elif name == "DanWood":
		
		level = "lower"
		
		def f(b, x):
			nb = len(x)
			y = [b[0]*x[n]**b[1] for n in range(nb)]
			return y
		
		def df(b, x):
			nb = len(x)
			dy = [[x[n]**b[1] for n in range(nb)],
			      [b[0]*x[n]**b[1]*math.log(x[n]) for n in range(nb)]]
			return dy
		
		X = [1.309E0, 1.471E0, 1.490E0, 1.565E0, 1.611E0, 1.680E0]
		Y = [2.138E0, 3.421E0, 3.597E0, 4.340E0, 4.882E0, 5.660E0]
		
		start = [[1.0, 5.0], [0.7, 4.0]]
		par = [7.6886226176E-01, 3.8604055871E+00]
		std_dev = [1.8281973860E-02, 5.1726610913E-02]
		SSR = 4.3173084083E-03
		res_std_dev = 3.2853114039E-02
	
	
	elif name == "Misra1b":
		
		level = "lower"
		
		def f(b, x):
			nb = len(x)
			y = [b[0] * (1.0-(1.0+b[1]*x[n]/2.0)**(-2)) for n in range(nb)]
			return y
		
		def df(b, x):
			nb = len(x)
			dy = [[(1.0-(1.0+b[1]*x[n]/2.0)**(-2)) for n in range(nb)],
			      [b[0]*(1.0+b[1]*x[n]/2.0)**(-3)*x[n] for n in range(nb)]]
			return dy
		
		X = [77.6E0, 114.9E0, 141.1E0, 190.8E0, 239.9E0, 289.0E0, 332.8E0, 378.4E0, 434.8E0, 477.3E0, 536.8E0, 593.1E0, 689.1E0, 760.0E0]
		Y = [10.07E0, 14.73E0, 17.94E0, 23.93E0, 29.61E0, 35.18E0, 40.02E0, 44.82E0, 50.76E0, 55.05E0, 61.01E0, 66.40E0, 75.47E0, 81.78E0]
		
		start = [[500.0, 0.0001], [300.0, 0.0002]]
		par = [3.3799746163E+02, 3.9039091287E-04]
		std_dev = [3.1643950207E+00, 4.2547321834E-06]
		SSR = 7.5464681533E-02
		res_std_dev = 7.9301471998E-02
	
	
	elif name == "Kirby2":
		
		level = "average"
		
		def f(b, x):
			nb = len(x)
			y = [(b[0] + b[1]*x[n] + b[2]*x[n]*x[n]) / (1.0 + b[3]*x[n] + b[4]*x[n]*x[n]) for n in range(nb)]
			return y
		
		def df(b, x):
			nb = len(x)
			dy = [[1.0 / (1.0 + b[3]*x[n] + b[4]*x[n]*x[n]) for n in range(nb)],
			      [x[n] / (1.0 + b[3]*x[n] + b[4]*x[n]*x[n]) for n in range(nb)],
			      [x[n]*x[n] / (1.0 + b[3]*x[n] + b[4]*x[n]*x[n]) for n in range(nb)],
			      [-(b[0] + b[1]*x[n] + b[2]*x[n]*x[n]) / (1.0 + b[3]*x[n] + b[4]*x[n]*x[n])**2 * x[n] for n in range(nb)],
			      [-(b[0] + b[1]*x[n] + b[2]*x[n]*x[n]) / (1.0 + b[3]*x[n] + b[4]*x[n]*x[n])**2 * x[n]*x[n] for n in range(nb)]]
			return dy
		
		X = [9.65E0, 10.74E0, 11.81E0, 12.88E0, 14.06E0, 15.28E0, 16.63E0, 18.19E0, 19.88E0, 21.84E0, 24.00E0, 26.25E0, 28.86E0, 31.85E0, 35.79E0, 40.18E0, 44.74E0, 49.53E0, 53.94E0, 58.29E0, 62.63E0, 67.03E0, 71.25E0, 75.22E0, 79.33E0, 83.56E0, 87.75E0, 91.93E0, 96.10E0, 100.28E0, 104.46E0, 108.66E0, 112.71E0, 116.88E0, 121.33E0, 125.79E0, 125.79E0, 128.74E0, 130.27E0, 133.33E0, 134.79E0, 137.93E0, 139.33E0, 142.46E0, 143.90E0, 146.91E0, 148.51E0, 151.41E0, 153.17E0, 155.97E0, 157.76E0, 160.56E0, 162.30E0, 165.21E0, 166.90E0, 169.92E0, 170.32E0, 171.54E0, 173.79E0, 174.57E0, 176.25E0, 177.34E0, 179.19E0, 181.02E0, 182.08E0, 183.88E0, 185.75E0, 186.80E0, 188.63E0, 190.45E0, 191.48E0, 193.35E0, 195.22E0, 196.23E0, 198.05E0, 199.97E0, 201.06E0, 202.83E0, 204.69E0, 205.86E0, 207.58E0, 209.50E0, 210.65E0, 212.33E0, 215.43E0, 217.16E0, 220.21E0, 221.98E0, 225.06E0, 226.79E0, 229.92E0, 231.69E0, 234.77E0, 236.60E0, 239.63E0, 241.50E0, 244.48E0, 246.40E0, 249.35E0, 251.32E0, 254.22E0, 256.24E0, 259.11E0, 261.18E0, 264.02E0, 266.13E0, 268.94E0, 271.09E0, 273.87E0, 276.08E0, 278.83E0, 281.08E0, 283.81E0, 286.11E0, 288.81E0, 291.08E0, 293.75E0, 295.99E0, 298.64E0, 300.84E0, 302.02E0, 303.48E0, 305.65E0, 308.27E0, 310.41E0, 313.01E0, 315.12E0, 317.71E0, 319.79E0, 322.36E0, 324.42E0, 326.98E0, 329.01E0, 331.56E0, 333.56E0, 336.10E0, 338.08E0, 340.60E0, 342.57E0, 345.08E0, 347.02E0, 349.52E0, 351.44E0, 353.93E0, 355.83E0, 358.32E0, 360.20E0, 362.67E0, 364.53E0, 367.00E0, 371.30E0]
		Y = [0.0082E0, 0.0112E0, 0.0149E0, 0.0198E0, 0.0248E0, 0.0324E0, 0.0420E0, 0.0549E0, 0.0719E0, 0.0963E0, 0.1291E0, 0.1710E0, 0.2314E0, 0.3227E0, 0.4809E0, 0.7084E0, 1.0220E0, 1.4580E0, 1.9520E0, 2.5410E0, 3.2230E0, 3.9990E0, 4.8520E0, 5.7320E0, 6.7270E0, 7.8350E0, 9.0250E0, 10.2670E0, 11.5780E0, 12.9440E0, 14.3770E0, 15.8560E0, 17.3310E0, 18.8850E0, 20.5750E0, 22.3200E0, 22.3030E0, 23.4600E0, 24.0600E0, 25.2720E0, 25.8530E0, 27.1100E0, 27.6580E0, 28.9240E0, 29.5110E0, 30.7100E0, 31.3500E0, 32.5200E0, 33.2300E0, 34.3300E0, 35.0600E0, 36.1700E0, 36.8400E0, 38.0100E0, 38.6700E0, 39.8700E0, 40.0300E0, 40.5000E0, 41.3700E0, 41.6700E0, 42.3100E0, 42.7300E0, 43.4600E0, 44.1400E0, 44.5500E0, 45.2200E0, 45.9200E0, 46.3000E0, 47.0000E0, 47.6800E0, 48.0600E0, 48.7400E0, 49.4100E0, 49.7600E0, 50.4300E0, 51.1100E0, 51.5000E0, 52.1200E0, 52.7600E0, 53.1800E0, 53.7800E0, 54.4600E0, 54.8300E0, 55.4000E0, 56.4300E0, 57.0300E0, 58.0000E0, 58.6100E0, 59.5800E0, 60.1100E0, 61.1000E0, 61.6500E0, 62.5900E0, 63.1200E0, 64.0300E0, 64.6200E0, 65.4900E0, 66.0300E0, 66.8900E0, 67.4200E0, 68.2300E0, 68.7700E0, 69.5900E0, 70.1100E0, 70.8600E0, 71.4300E0, 72.1600E0, 72.7000E0, 73.4000E0, 73.9300E0, 74.6000E0, 75.1600E0, 75.8200E0, 76.3400E0, 76.9800E0, 77.4800E0, 78.0800E0, 78.6000E0, 79.1700E0, 79.6200E0, 79.8800E0, 80.1900E0, 80.6600E0, 81.2200E0, 81.6600E0, 82.1600E0, 82.5900E0, 83.1400E0, 83.5000E0, 84.0000E0, 84.4000E0, 84.8900E0, 85.2600E0, 85.7400E0, 86.0700E0, 86.5400E0, 86.8900E0, 87.3200E0, 87.6500E0, 88.1000E0, 88.4300E0, 88.8300E0, 89.1200E0, 89.5400E0, 89.8500E0, 90.2500E0, 90.5500E0, 90.9300E0, 91.2000E0, 91.5500E0, 92.2000E0]
		
		start = [[2.0, -0.1, 0.003, -0.001, 0.00001], [1.5, -0.15, 0.0025, -0.0015, 0.00002]]
		par = [1.6745063063E+00, -1.3927397867E-01, 2.5961181191E-03, -1.7241811870E-03, 2.1664802578E-05]
		std_dev = [8.7989634338E-02, 4.1182041386E-03, 4.1856520458E-05, 5.8931897355E-05, 2.0129761919E-07]
		SSR = 3.9050739624E+00
		res_std_dev = 1.6354535131E-01
	
	
	elif name == "Hahn1":
		
		level = "average"
		
		def f(b, x):
			nb = len(x)
			y = [(b[0]+b[1]*x[n]+b[2]*x[n]*x[n]+b[3]*x[n]*x[n]*x[n]) / (1.0+b[4]*x[n]+b[5]*x[n]*x[n]+b[6]*x[n]*x[n]*x[n]) for n in range(nb)]
			return y
		
		def df(b, x):
			nb = len(x)
			dy = [[1.0 / (1.0+b[4]*x[n]+b[5]*x[n]*x[n]+b[6]*x[n]*x[n]*x[n]) for n in range(nb)],
			      [x[n] / (1.0+b[4]*x[n]+b[5]*x[n]*x[n]+b[6]*x[n]*x[n]*x[n]) for n in range(nb)],
			      [x[n]*x[n] / (1.0+b[4]*x[n]+b[5]*x[n]*x[n]+b[6]*x[n]*x[n]*x[n]) for n in range(nb)],
			      [x[n]*x[n]*x[n] / (1.0+b[4]*x[n]+b[5]*x[n]*x[n]+b[6]*x[n]*x[n]*x[n]) for n in range(nb)],
			      [-(b[0]+b[1]*x[n]+b[2]*x[n]*x[n]+b[3]*x[n]*x[n]*x[n]) / (1.0+b[4]*x[n]+b[5]*x[n]*x[n]+b[6]*x[n]*x[n]*x[n])**2 * x[n] for n in range(nb)],
			      [-(b[0]+b[1]*x[n]+b[2]*x[n]*x[n]+b[3]*x[n]*x[n]*x[n]) / (1.0+b[4]*x[n]+b[5]*x[n]*x[n]+b[6]*x[n]*x[n]*x[n])**2 * x[n]*x[n] for n in range(nb)],
			      [-(b[0]+b[1]*x[n]+b[2]*x[n]*x[n]+b[3]*x[n]*x[n]*x[n]) / (1.0+b[4]*x[n]+b[5]*x[n]*x[n]+b[6]*x[n]*x[n]*x[n])**2 * x[n]*x[n]*x[n] for n in range(nb)]]
			return dy
		
		X = [24.41E0, 34.82E0, 44.09E0, 45.07E0, 54.98E0, 65.51E0, 70.53E0, 75.70E0, 89.57E0, 91.14E0, 96.40E0, 97.19E0, 114.26E0, 120.25E0, 127.08E0, 133.55E0, 133.61E0, 158.67E0, 172.74E0, 171.31E0, 202.14E0, 220.55E0, 221.05E0, 221.39E0, 250.99E0, 268.99E0, 271.80E0, 271.97E0, 321.31E0, 321.69E0, 330.14E0, 333.03E0, 333.47E0, 340.77E0, 345.65E0, 373.11E0, 373.79E0, 411.82E0, 419.51E0, 421.59E0, 422.02E0, 422.47E0, 422.61E0, 441.75E0, 447.41E0, 448.7E0, 472.89E0, 476.69E0, 522.47E0, 522.62E0, 524.43E0, 546.75E0, 549.53E0, 575.29E0, 576.00E0, 625.55E0, 20.15E0, 28.78E0, 29.57E0, 37.41E0, 39.12E0, 50.24E0, 61.38E0, 66.25E0, 73.42E0, 95.52E0, 107.32E0, 122.04E0, 134.03E0, 163.19E0, 163.48E0, 175.70E0, 179.86E0, 211.27E0, 217.78E0, 219.14E0, 262.52E0, 268.01E0, 268.62E0, 336.25E0, 337.23E0, 339.33E0, 427.38E0, 428.58E0, 432.68E0, 528.99E0, 531.08E0, 628.34E0, 253.24E0, 273.13E0, 273.66E0, 282.10E0, 346.62E0, 347.19E0, 348.78E0, 351.18E0, 450.10E0, 450.35E0, 451.92E0, 455.56E0, 552.22E0, 553.56E0, 555.74E0, 652.59E0, 656.20E0, 14.13E0, 20.41E0, 31.30E0, 33.84E0, 39.70E0, 48.83E0, 54.50E0, 60.41E0, 72.77E0, 75.25E0, 86.84E0, 94.88E0, 96.40E0, 117.37E0, 139.08E0, 147.73E0, 158.63E0, 161.84E0, 192.11E0, 206.76E0, 209.07E0, 213.32E0, 226.44E0, 237.12E0, 330.90E0, 358.72E0, 370.77E0, 372.72E0, 396.24E0, 416.59E0, 484.02E0, 495.47E0, 514.78E0, 515.65E0, 519.47E0, 544.47E0, 560.11E0, 620.77E0, 18.97E0, 28.93E0, 33.91E0, 40.03E0, 44.66E0, 49.87E0, 55.16E0, 60.90E0, 72.08E0, 85.15E0, 97.06E0, 119.63E0, 133.27E0, 143.84E0, 161.91E0, 180.67E0, 198.44E0, 226.86E0, 229.65E0, 258.27E0, 273.77E0, 339.15E0, 350.13E0, 362.75E0, 371.03E0, 393.32E0, 448.53E0, 473.78E0, 511.12E0, 524.70E0, 548.75E0, 551.64E0, 574.02E0, 623.86E0, 21.46E0, 24.33E0, 33.43E0, 39.22E0, 44.18E0, 55.02E0, 94.33E0, 96.44E0, 118.82E0, 128.48E0, 141.94E0, 156.92E0, 171.65E0, 190.00E0, 223.26E0, 223.88E0, 231.50E0, 265.05E0, 269.44E0, 271.78E0, 273.46E0, 334.61E0, 339.79E0, 349.52E0, 358.18E0, 377.98E0, 394.77E0, 429.66E0, 468.22E0, 487.27E0, 519.54E0, 523.03E0, 612.99E0, 638.59E0, 641.36E0, 622.05E0, 631.50E0, 663.97E0, 646.9E0, 748.29E0, 749.21E0, 750.14E0, 647.04E0, 646.89E0, 746.9E0, 748.43E0, 747.35E0, 749.27E0, 647.61E0, 747.78E0, 750.51E0, 851.37E0, 845.97E0, 847.54E0, 849.93E0, 851.61E0, 849.75E0, 850.98E0, 848.23E0]
		Y = [.591E0, 1.547E0, 2.902E0, 2.894E0, 4.703E0, 6.307E0, 7.03E0, 7.898E0, 9.470E0, 9.484E0, 10.072E0, 10.163E0, 11.615E0, 12.005E0, 12.478E0, 12.982E0, 12.970E0, 13.926E0, 14.452E0, 14.404E0, 15.190E0, 15.550E0, 15.528E0, 15.499E0, 16.131E0, 16.438E0, 16.387E0, 16.549E0, 16.872E0, 16.830E0, 16.926E0, 16.907E0, 16.966E0, 17.060E0, 17.122E0, 17.311E0, 17.355E0, 17.668E0, 17.767E0, 17.803E0, 17.765E0, 17.768E0, 17.736E0, 17.858E0, 17.877E0, 17.912E0, 18.046E0, 18.085E0, 18.291E0, 18.357E0, 18.426E0, 18.584E0, 18.610E0, 18.870E0, 18.795E0, 19.111E0, .367E0, .796E0, 0.892E0, 1.903E0, 2.150E0, 3.697E0, 5.870E0, 6.421E0, 7.422E0, 9.944E0, 11.023E0, 11.87E0, 12.786E0, 14.067E0, 13.974E0, 14.462E0, 14.464E0, 15.381E0, 15.483E0, 15.59E0, 16.075E0, 16.347E0, 16.181E0, 16.915E0, 17.003E0, 16.978E0, 17.756E0, 17.808E0, 17.868E0, 18.481E0, 18.486E0, 19.090E0, 16.062E0, 16.337E0, 16.345E0, 16.388E0, 17.159E0, 17.116E0, 17.164E0, 17.123E0, 17.979E0, 17.974E0, 18.007E0, 17.993E0, 18.523E0, 18.669E0, 18.617E0, 19.371E0, 19.330E0, 0.080E0, 0.248E0, 1.089E0, 1.418E0, 2.278E0, 3.624E0, 4.574E0, 5.556E0, 7.267E0, 7.695E0, 9.136E0, 9.959E0, 9.957E0, 11.600E0, 13.138E0, 13.564E0, 13.871E0, 13.994E0, 14.947E0, 15.473E0, 15.379E0, 15.455E0, 15.908E0, 16.114E0, 17.071E0, 17.135E0, 17.282E0, 17.368E0, 17.483E0, 17.764E0, 18.185E0, 18.271E0, 18.236E0, 18.237E0, 18.523E0, 18.627E0, 18.665E0, 19.086E0, 0.214E0, 0.943E0, 1.429E0, 2.241E0, 2.951E0, 3.782E0, 4.757E0, 5.602E0, 7.169E0, 8.920E0, 10.055E0, 12.035E0, 12.861E0, 13.436E0, 14.167E0, 14.755E0, 15.168E0, 15.651E0, 15.746E0, 16.216E0, 16.445E0, 16.965E0, 17.121E0, 17.206E0, 17.250E0, 17.339E0, 17.793E0, 18.123E0, 18.49E0, 18.566E0, 18.645E0, 18.706E0, 18.924E0, 19.1E0, 0.375E0, 0.471E0, 1.504E0, 2.204E0, 2.813E0, 4.765E0, 9.835E0, 10.040E0, 11.946E0, 12.596E0, 13.303E0, 13.922E0, 14.440E0, 14.951E0, 15.627E0, 15.639E0, 15.814E0, 16.315E0, 16.334E0, 16.430E0, 16.423E0, 17.024E0, 17.009E0, 17.165E0, 17.134E0, 17.349E0, 17.576E0, 17.848E0, 18.090E0, 18.276E0, 18.404E0, 18.519E0, 19.133E0, 19.074E0, 19.239E0, 19.280E0, 19.101E0, 19.398E0, 19.252E0, 19.89E0, 20.007E0, 19.929E0, 19.268E0, 19.324E0, 20.049E0, 20.107E0, 20.062E0, 20.065E0, 19.286E0, 19.972E0, 20.088E0, 20.743E0, 20.83E0, 20.935E0, 21.035E0, 20.93E0, 21.074E0, 21.085E0, 20.935E0]
		
		start = [[10.0, -1.0, 0.05, -0.00001, -0.05, 0.001, -0.000001], [1.0, -0.1, 0.005, -0.000001, -0.005, 0.0001, -0.0000001]]
		par = [1.0776351733E+00, -1.2269296921E-01, 4.0863750610E-03, -1.4262662514E-06, -5.7609940901E-03, 2.4053735503E-04, -1.2314450199E-07]
		std_dev = [1.7070154742E-01, 1.2000289189E-02, 2.2508314937E-04, 2.7578037666E-07, 2.4712888219E-04, 1.0449373768E-05, 1.3027335327E-08]
		SSR = 1.5324382854E+00
		res_std_dev = 8.1803852243E-02
	
	
	elif name == "Nelson":
		
		level = "average"
		
		def f(b, x):
			nb = len(x[0])
			y = [b[0] - b[1]*x[0][n] * math.exp(-b[2]*x[1][n]) for n in range(nb)]
			return y
		
		def df(b, x):
			nb = len(x[0])
			dy = [[1.0 for n in range(nb)],
			      [- x[0][n] * math.exp(-b[2]*x[1][n]) for n in range(nb)],
			      [b[1]*x[0][n]*x[1][n] * math.exp(-b[2]*x[1][n]) for n in range(nb)]]
			return dy
		
		x1 = [1E0, 1E0, 1E0, 1E0, 1E0, 1E0, 1E0, 1E0, 1E0, 1E0, 1E0, 1E0, 1E0, 1E0, 1E0, 1E0, 2E0, 2E0, 2E0, 2E0, 2E0, 2E0, 2E0, 2E0, 2E0, 2E0, 2E0, 2E0, 2E0, 2E0, 2E0, 2E0, 4E0, 4E0, 4E0, 4E0, 4E0, 4E0, 4E0, 4E0, 4E0, 4E0, 4E0, 4E0, 4E0, 4E0, 4E0, 4E0, 8E0, 8E0, 8E0, 8E0, 8E0, 8E0, 8E0, 8E0, 8E0, 8E0, 8E0, 8E0, 8E0, 8E0, 8E0, 8E0, 16E0, 16E0, 16E0, 16E0, 16E0, 16E0, 16E0, 16E0, 16E0, 16E0, 16E0, 16E0, 16E0, 16E0, 16E0, 16E0, 32E0, 32E0, 32E0, 32E0, 32E0, 32E0, 32E0, 32E0, 32E0, 32E0, 32E0, 32E0, 32E0, 32E0, 32E0, 32E0, 48E0, 48E0, 48E0, 48E0, 48E0, 48E0, 48E0, 48E0, 48E0, 48E0, 48E0, 48E0, 48E0, 48E0, 48E0, 48E0, 64E0, 64E0, 64E0, 64E0, 64E0, 64E0, 64E0, 64E0, 64E0, 64E0, 64E0, 64E0, 64E0, 64E0, 64E0, 64E0]
		x2 = [180E0, 180E0, 180E0, 180E0, 225E0, 225E0, 225E0, 225E0, 250E0, 250E0, 250E0, 250E0, 275E0, 275E0, 275E0, 275E0, 180E0, 180E0, 180E0, 180E0, 225E0, 225E0, 225E0, 225E0, 250E0, 250E0, 250E0, 250E0, 275E0, 275E0, 275E0, 275E0, 180E0, 180E0, 180E0, 180E0, 225E0, 225E0, 225E0, 225E0, 250E0, 250E0, 250E0, 250E0, 275E0, 275E0, 275E0, 275E0, 180E0, 180E0, 180E0, 180E0, 225E0, 225E0, 225E0, 225E0, 250E0, 250E0, 250E0, 250E0, 275E0, 275E0, 275E0, 275E0, 180E0, 180E0, 180E0, 180E0, 225E0, 225E0, 225E0, 225E0, 250E0, 250E0, 250E0, 250E0, 275E0, 275E0, 275E0, 275E0, 180E0, 180E0, 180E0, 180E0, 225E0, 225E0, 225E0, 225E0, 250E0, 250E0, 250E0, 250E0, 275E0, 275E0, 275E0, 275E0, 180E0, 180E0, 180E0, 180E0, 225E0, 225E0, 225E0, 225E0, 250E0, 250E0, 250E0, 250E0, 275E0, 275E0, 275E0, 275E0, 180E0, 180E0, 180E0, 180E0, 225E0, 225E0, 225E0, 225E0, 250E0, 250E0, 250E0, 250E0, 275E0, 275E0, 275E0, 275E0]
		X = [x1, x2]
		y = [15.00E0, 17.00E0, 15.50E0, 16.50E0, 15.50E0, 15.00E0, 16.00E0, 14.50E0, 15.00E0, 14.50E0, 12.50E0, 11.00E0, 14.00E0, 13.00E0, 14.00E0, 11.50E0, 14.00E0, 16.00E0, 13.00E0, 13.50E0, 13.00E0, 13.50E0, 12.50E0, 12.50E0, 12.50E0, 12.00E0, 11.50E0, 12.00E0, 13.00E0, 11.50E0, 13.00E0, 12.50E0, 13.50E0, 17.50E0, 17.50E0, 13.50E0, 12.50E0, 12.50E0, 15.00E0, 13.00E0, 12.00E0, 13.00E0, 12.00E0, 13.50E0, 10.00E0, 11.50E0, 11.00E0, 9.50E0, 15.00E0, 15.00E0, 15.50E0, 16.00E0, 13.00E0, 10.50E0, 13.50E0, 14.00E0, 12.50E0, 12.00E0, 11.50E0, 11.50E0, 6.50E0, 5.50E0, 6.00E0, 6.00E0, 18.50E0, 17.00E0, 15.30E0, 16.00E0, 13.00E0, 14.00E0, 12.50E0, 11.00E0, 12.00E0, 12.00E0, 11.50E0, 12.00E0, 6.00E0, 6.00E0, 5.00E0, 5.50E0, 12.50E0, 13.00E0, 16.00E0, 12.00E0, 11.00E0, 9.50E0, 11.00E0, 11.00E0, 11.00E0, 10.00E0, 10.50E0, 10.50E0, 2.70E0, 2.70E0, 2.50E0, 2.40E0, 13.00E0, 13.50E0, 16.50E0, 13.60E0, 11.50E0, 10.50E0, 13.50E0, 12.00E0, 7.00E0, 6.90E0, 8.80E0, 7.90E0, 1.20E0, 1.50E0, 1.00E0, 1.50E0, 13.00E0, 12.50E0, 16.50E0, 16.00E0, 11.00E0, 11.50E0, 10.50E0, 10.00E0, 7.27E0, 7.50E0, 6.70E0, 7.60E0, 1.50E0, 1.00E0, 1.20E0, 1.20E0]
		nb_pts = len(y)
		Y = [math.log(y[n]) for n in range(nb_pts)]
		
		start = [[2.0, 0.0001, -0.01], [2.5, 0.000000005, -0.05]]
		par = [2.5906836021E+00, 5.6177717026E-09, -5.7701013174E-02]
		std_dev = [1.9149996413E-02, 6.1124096540E-09, 3.9572366543E-03]
		SSR = 3.7976833176E+00
		res_std_dev = 1.7430280130E-01
	
	
	elif name == "MGH17":
		
		level = "average"
		
		def f(b, x):
			nb = len(x)
			y = [b[0] + b[1]*math.exp(-x[n]*b[3]) + b[2]*math.exp(-x[n]*b[4]) for n in range(nb)]
			return y
		
		def df(b, x):
			nb = len(x)
			dy = [[1.0 for n in range(nb)],
			      [math.exp(-x[n]*b[3]) for n in range(nb)],
			      [math.exp(-x[n]*b[4]) for n in range(nb)],
			      [-b[1]*x[n]*math.exp(-x[n]*b[3]) for n in range(nb)],
			      [-b[2]*x[n]*math.exp(-x[n]*b[4]) for n in range(nb)]]
			return dy
		
		X = [0.000000E+00, 1.000000E+01, 2.000000E+01, 3.000000E+01, 4.000000E+01, 5.000000E+01, 6.000000E+01, 7.000000E+01, 8.000000E+01, 9.000000E+01, 1.000000E+02, 1.100000E+02, 1.200000E+02, 1.300000E+02, 1.400000E+02, 1.500000E+02, 1.600000E+02, 1.700000E+02, 1.800000E+02, 1.900000E+02, 2.000000E+02, 2.100000E+02, 2.200000E+02, 2.300000E+02, 2.400000E+02, 2.500000E+02, 2.600000E+02, 2.700000E+02, 2.800000E+02, 2.900000E+02, 3.000000E+02, 3.100000E+02, 3.200000E+02]
		Y = [8.440000E-01, 9.080000E-01, 9.320000E-01, 9.360000E-01, 9.250000E-01, 9.080000E-01, 8.810000E-01, 8.500000E-01, 8.180000E-01, 7.840000E-01, 7.510000E-01, 7.180000E-01, 6.850000E-01, 6.580000E-01, 6.280000E-01, 6.030000E-01, 5.800000E-01, 5.580000E-01, 5.380000E-01, 5.220000E-01, 5.060000E-01, 4.900000E-01, 4.780000E-01, 4.670000E-01, 4.570000E-01, 4.480000E-01, 4.380000E-01, 4.310000E-01, 4.240000E-01, 4.200000E-01, 4.140000E-01, 4.110000E-01, 4.060000E-01]
		
		start = [[50.0, 150.0, -100.0, 1.0, 2.0], [0.5, 1.5, -1.0, 0.01, 0.02]]
		par = [3.7541005211E-01, 1.9358469127E+00, -1.4646871366E+00, 1.2867534640E-02, 2.2122699662E-02]
		std_dev = [2.0723153551E-03, 2.2031669222E-01, 2.2175707739E-01, 4.4861358114E-04, 8.9471996575E-04]
		SSR = 5.4648946975E-05
		res_std_dev = 1.3970497866E-03
	
	
	elif name == "Lanczos1":
		
		level = "average"
		
		def f(b, x):
			nb = len(x)
			y = [b[0]*math.exp(-b[1]*x[n]) + b[2]*math.exp(-b[3]*x[n]) + b[4]*math.exp(-b[5]*x[n]) for n in range(nb)]
			return y
		
		def df(b, x):
			nb = len(x)
			dy = [[math.exp(-b[1]*x[n]) for n in range(nb)],
			      [-b[0]*x[n]*math.exp(-b[1]*x[n]) for n in range(nb)],
			      [math.exp(-b[3]*x[n]) for n in range(nb)],
			      [-b[2]*x[n]*math.exp(-b[3]*x[n]) for n in range(nb)],
			      [math.exp(-b[5]*x[n]) for n in range(nb)],
			      [-b[4]*x[n]*math.exp(-b[5]*x[n]) for n in range(nb)]]
			return dy
		
		X = [0.000000000000E+00, 5.000000000000E-02, 1.000000000000E-01, 1.500000000000E-01, 2.000000000000E-01, 2.500000000000E-01, 3.000000000000E-01, 3.500000000000E-01, 4.000000000000E-01, 4.500000000000E-01, 5.000000000000E-01, 5.500000000000E-01, 6.000000000000E-01, 6.500000000000E-01, 7.000000000000E-01, 7.500000000000E-01, 8.000000000000E-01, 8.500000000000E-01, 9.000000000000E-01, 9.500000000000E-01, 1.000000000000E+00, 1.050000000000E+00, 1.100000000000E+00, 1.150000000000E+00]
		Y = [2.513400000000E+00, 2.044333373291E+00, 1.668404436564E+00, 1.366418021208E+00, 1.123232487372E+00, 9.268897180037E-01, 7.679338563728E-01, 6.388775523106E-01, 5.337835317402E-01, 4.479363617347E-01, 3.775847884350E-01, 3.197393199326E-01, 2.720130773746E-01, 2.324965529032E-01, 1.996589546065E-01, 1.722704126914E-01, 1.493405660168E-01, 1.300700206922E-01, 1.138119324644E-01, 1.000415587559E-01, 8.833209084540E-02, 7.833544019350E-02, 6.976693743449E-02, 6.239312536719E-02]
		
		start = [[1.2, 0.3, 5.6, 5.5, 6.5, 7.6], [0.5, 0.7, 3.6, 4.2, 4.0, 6.3]]
		par = [9.5100000027E-02, 1.0000000001E+00, 8.6070000013E-01, 3.0000000002E+00, 1.5575999998E+00, 5.0000000001E+00]
		std_dev = [5.3347304234E-11, 2.7473038179E-10, 1.3576062225E-10, 3.3308253069E-10, 1.8815731448E-10, 1.1057500538E-10]
		SSR = 1.4307867721E-25
		res_std_dev = 8.9156129349E-14
	
	
	elif name == "Lanczos2":
		
		level = "average"
		
		def f(b, x):
			nb = len(x)
			y = [b[0]*math.exp(-b[1]*x[n]) + b[2]*math.exp(-b[3]*x[n]) + b[4]*math.exp(-b[5]*x[n]) for n in range(nb)]
			return y
		
		def df(b, x):
			nb = len(x)
			dy = [[math.exp(-b[1]*x[n]) for n in range(nb)],
			      [-b[0]*x[n]*math.exp(-b[1]*x[n]) for n in range(nb)],
			      [math.exp(-b[3]*x[n]) for n in range(nb)],
			      [-b[2]*x[n]*math.exp(-b[3]*x[n]) for n in range(nb)],
			      [math.exp(-b[5]*x[n]) for n in range(nb)],
			      [-b[4]*x[n]*math.exp(-b[5]*x[n]) for n in range(nb)]]
			return dy
		
		X = [0.00000E+00, 5.00000E-02, 1.00000E-01, 1.50000E-01, 2.00000E-01, 2.50000E-01, 3.00000E-01, 3.50000E-01, 4.00000E-01, 4.50000E-01, 5.00000E-01, 5.50000E-01, 6.00000E-01, 6.50000E-01, 7.00000E-01, 7.50000E-01, 8.00000E-01, 8.50000E-01, 9.00000E-01, 9.50000E-01, 1.00000E+00, 1.05000E+00, 1.10000E+00, 1.15000E+00]
		Y = [2.51340E+00, 2.04433E+00, 1.66840E+00, 1.36642E+00, 1.12323E+00, 9.26890E-01, 7.67934E-01, 6.38878E-01, 5.33784E-01, 4.47936E-01, 3.77585E-01, 3.19739E-01, 2.72013E-01, 2.32497E-01, 1.99659E-01, 1.72270E-01, 1.49341E-01, 1.30070E-01, 1.13812E-01, 1.00042E-01, 8.83321E-02, 7.83354E-02, 6.97669E-02, 6.23931E-02]
		
		start = [[1.2, 0.3, 5.6, 5.5, 6.5, 7.6], [0.5, 0.7, 3.6, 4.2, 4.0, 6.3]]
		par = [9.6251029939E-02, 1.0057332849E+00, 8.6424689056E-01, 3.0078283915E+00, 1.5529016879E+00, 5.0028798100E+00]
		std_dev = [6.6770575477E-04, 3.3989646176E-03, 1.7185846685E-03, 4.1707005856E-03, 2.3744381417E-03, 1.3958787284E-03]
		SSR = 2.2299428125E-11
		res_std_dev = 1.1130395851E-06
	
	
	elif name == "Gauss3":
		
		level = "average"
		
		def f(b, x):
			nb = len(x)
			y = [b[0]*math.exp(-b[1]*x[n]) + b[2]*math.exp(-((x[n]-b[3])/b[4])**2) + b[5]*math.exp(-((x[n]-b[6])/b[7])**2) for n in range(nb)]
			return y
		
		def df(b, x):
			nb = len(x)
			dy = [[math.exp(-b[1]*x[n]) for n in range(nb)],
			      [-b[0]*x[n]*math.exp(-b[1]*x[n]) for n in range(nb)],
			      [math.exp(-((x[n]-b[3])/b[4])**2) for n in range(nb)],
			      [b[2]*math.exp(-((x[n]-b[3])/b[4])**2)*2.0*(x[n]-b[3])/b[4]**2 for n in range(nb)],
			      [b[2]*math.exp(-((x[n]-b[3])/b[4])**2)*2.0*(x[n]-b[3])**2/b[4]**3 for n in range(nb)],
			      [math.exp(-((x[n]-b[6])/b[7])**2) for n in range(nb)],
			      [b[5]*math.exp(-((x[n]-b[6])/b[7])**2)*2.0*(x[n]-b[6])/b[7]**2 for n in range(nb)],
			      [b[5]*math.exp(-((x[n]-b[6])/b[7])**2)*2.0*(x[n]-b[6])**2/b[7]**3 for n in range(nb)]]
			return dy
		
		X = [1.000000, 2.000000, 3.000000, 4.000000, 5.000000, 6.000000, 7.000000, 8.000000, 9.000000, 10.000000, 11.00000, 12.00000, 13.00000, 14.00000, 15.00000, 16.00000, 17.00000, 18.00000, 19.00000, 20.00000, 21.00000, 22.00000, 23.00000, 24.00000, 25.00000, 26.00000, 27.00000, 28.00000, 29.00000, 30.00000, 31.00000, 32.00000, 33.00000, 34.00000, 35.00000, 36.00000, 37.00000, 38.00000, 39.00000, 40.00000, 41.00000, 42.00000, 43.00000, 44.00000, 45.00000, 46.00000, 47.00000, 48.00000, 49.00000, 50.00000, 51.00000, 52.00000, 53.00000, 54.00000, 55.00000, 56.00000, 57.00000, 58.00000, 59.00000, 60.00000, 61.00000, 62.00000, 63.00000, 64.00000, 65.00000, 66.00000, 67.00000, 68.00000, 69.00000, 70.00000, 71.00000, 72.00000, 73.00000, 74.00000, 75.00000, 76.00000, 77.00000, 78.00000, 79.00000, 80.00000, 81.00000, 82.00000, 83.00000, 84.00000, 85.00000, 86.00000, 87.00000, 88.00000, 89.00000, 90.00000, 91.00000, 92.00000, 93.00000, 94.00000, 95.00000, 96.00000, 97.00000, 98.00000, 99.00000, 100.00000, 101.00000, 102.00000, 103.00000, 104.00000, 105.00000, 106.0000, 107.0000, 108.0000, 109.0000, 110.0000, 111.0000, 112.0000, 113.0000, 114.0000, 115.0000, 116.0000, 117.0000, 118.0000, 119.0000, 120.0000, 121.0000, 122.0000, 123.0000, 124.0000, 125.0000, 126.0000, 127.0000, 128.0000, 129.0000, 130.0000, 131.0000, 132.0000, 133.0000, 134.0000, 135.0000, 136.0000, 137.0000, 138.0000, 139.0000, 140.0000, 141.0000, 142.0000, 143.0000, 144.0000, 145.0000, 146.0000, 147.0000, 148.0000, 149.0000, 150.0000, 151.0000, 152.0000, 153.0000, 154.0000, 155.0000, 156.0000, 157.0000, 158.0000, 159.0000, 160.0000, 161.0000, 162.0000, 163.0000, 164.0000, 165.0000, 166.0000, 167.0000, 168.0000, 169.0000, 170.0000, 171.0000, 172.0000, 173.0000, 174.0000, 175.0000, 176.0000, 177.0000, 178.0000, 179.0000, 180.0000, 181.0000, 182.0000, 183.0000, 184.0000, 185.0000, 186.0000, 187.0000, 188.0000, 189.0000, 190.0000, 191.0000, 192.0000, 193.0000, 194.0000, 195.0000, 196.0000, 197.0000, 198.0000, 199.0000, 200.0000, 201.0000, 202.0000, 203.0000, 204.0000, 205.0000, 206.0000, 207.0000, 208.0000, 209.0000, 210.0000, 211.0000, 212.0000, 213.0000, 214.0000, 215.0000, 216.0000, 217.0000, 218.0000, 219.0000, 220.0000, 221.0000, 222.0000, 223.0000, 224.0000, 225.0000, 226.0000, 227.0000, 228.0000, 229.0000, 230.0000, 231.0000, 232.0000, 233.0000, 234.0000, 235.0000, 236.0000, 237.0000, 238.0000, 239.0000, 240.0000, 241.0000, 242.0000, 243.0000, 244.0000, 245.0000, 246.0000, 247.0000, 248.0000, 249.0000, 250.0000]
		Y = [97.58776, 97.76344, 96.56705, 92.52037, 91.15097, 95.21728, 90.21355, 89.29235, 91.51479, 89.60965, 86.56187, 85.55315, 87.13053, 85.67938, 80.04849, 82.18922, 87.24078, 80.79401, 81.28564, 81.56932, 79.22703, 79.43259, 77.90174, 76.75438, 77.17338, 74.27296, 73.11830, 73.84732, 72.47746, 71.92128, 66.91962, 67.93554, 69.55841, 69.06592, 66.53371, 63.87094, 69.70526, 63.59295, 63.35509, 59.99747, 62.64843, 65.77345, 59.10141, 56.57750, 61.15313, 54.30767, 62.83535, 56.52957, 56.98427, 58.11459, 58.69576, 58.23322, 54.90490, 57.91442, 56.96629, 51.13831, 49.27123, 52.92668, 54.47693, 51.81710, 51.05401, 52.51731, 51.83710, 54.48196, 49.05859, 50.52315, 50.32755, 46.44419, 50.89281, 52.13203, 49.78741, 49.01637, 54.18198, 53.17456, 53.20827, 57.43459, 51.95282, 54.20282, 57.46687, 53.60268, 58.86728, 57.66652, 63.71034, 65.24244, 65.10878, 69.96313, 68.85475, 73.32574, 76.21241, 78.06311, 75.37701, 87.54449, 89.50588, 95.82098, 97.48390, 100.86070, 102.48510, 105.7311, 111.3489, 111.0305, 110.1920, 118.3581, 118.8086, 122.4249, 124.0953, 125.9337, 127.8533, 131.0361, 133.3343, 135.1278, 131.7113, 131.9151, 132.1107, 127.6898, 133.2148, 128.2296, 133.5902, 127.2539, 128.3482, 124.8694, 124.6031, 117.0648, 118.1966, 119.5408, 114.7946, 114.2780, 120.3484, 114.8647, 111.6514, 110.1826, 108.4461, 109.0571, 106.5308, 109.4691, 106.8709, 107.3192, 106.9000, 109.6526, 107.1602, 108.2509, 104.96310, 109.3601, 107.6696, 99.77286, 104.96440, 106.1376, 106.5816, 100.12860, 101.66910, 96.44254, 97.34169, 96.97412, 90.73460, 93.37949, 82.12331, 83.01657, 78.87360, 74.86971, 72.79341, 65.14744, 67.02127, 60.16136, 57.13996, 54.05769, 50.42265, 47.82430, 42.85748, 42.45495, 38.30808, 36.95794, 33.94543, 34.19017, 31.66097, 23.56172, 29.61143, 23.88765, 22.49812, 24.86901, 17.29481, 18.09291, 15.34813, 14.77997, 13.87832, 12.88891, 16.20763, 16.29024, 15.29712, 14.97839, 12.11330, 14.24168, 12.53824, 15.19818, 11.70478, 15.83745, 10.035850, 9.307574, 12.86800, 8.571671, 11.60415, 12.42772, 11.23627, 11.13198, 7.761117, 6.758250, 14.23375, 10.63876, 8.893581, 11.55398, 11.57221, 11.58347, 9.724857, 11.43854, 11.22636, 10.170150, 12.50765, 6.200494, 9.018902, 10.80557, 13.09591, 3.914033, 9.567723, 8.038338, 10.230960, 9.367358, 7.695937, 6.118552, 8.793192, 7.796682, 12.45064, 10.61601, 6.001000, 6.765096, 8.764652, 4.586417, 8.390782, 7.209201, 10.012090, 7.327461, 6.525136, 2.840065, 10.323710, 4.790035, 8.376431, 6.263980, 2.705892, 8.362109, 8.983507, 3.362469, 1.182678, 4.875312]
		
		start = [[94.9, 0.009, 90.1, 113.0, 20.0, 73.8, 140.0, 20.0], [96.0, 0.0096, 80.0, 110.0, 25.0, 74.0, 139.0, 25.0]]
		par = [9.8940368970E+01, 1.0945879335E-02, 1.0069553078E+02, 1.1163619459E+02, 2.3300500029E+01, 7.3705031418E+01, 1.4776164251E+02, 1.9668221230E+01]
		std_dev = [5.3005192833E-01, 1.2554058911E-04, 8.1256587317E-01, 3.5317859757E-01, 3.6584783023E-01, 1.2091239082E+00, 4.0488183351E-01, 3.7806634336E-01]
		SSR = 1.2444846360E+03
		res_std_dev = 2.2677077625E+00
	
	
	elif name == "Misra1c":
		
		level = "average"
		
		def f(b, x):
			nb = len(x)
			y = [b[0] * (1.0-(1.0+2.0*b[1]*x[n])**(-0.5)) for n in range(nb)]
			return y
		
		def df(b, x):
			nb = len(x)
			dy = [[(1.0-(1.0+2.0*b[1]*x[n])**(-0.5)) for n in range(nb)],
			      [b[0] * (1.0+2.0*b[1]*x[n])**(-1.5) * x[n] for n in range(nb)]]
			return dy
		
		X = [77.6E0, 114.9E0, 141.1E0, 190.8E0, 239.9E0, 289.0E0, 332.8E0, 378.4E0, 434.8E0, 477.3E0, 536.8E0, 593.1E0, 689.1E0, 760.0E0]
		Y = [10.07E0, 14.73E0, 17.94E0, 23.93E0, 29.61E0, 35.18E0, 40.02E0, 44.82E0, 50.76E0, 55.05E0, 61.01E0, 66.40E0, 75.47E0, 81.78E0]
		
		start = [[500.0, 0.0001], [600.0, 0.0002]]
		par = [6.3642725809E+02, 2.0813627256E-04]
		std_dev = [4.6638326572E+00, 1.7728423155E-06]
		SSR = 4.0966836971E-02
		res_std_dev = 5.8428615257E-02
	
	
	elif name == "Misra1d":
		
		level = "average"
		
		def f(b, x):
			nb = len(x)
			y = [b[0]*b[1]*x[n] / (1.0+b[1]*x[n]) for n in range(nb)]
			return y
		
		def df(b, x):
			nb = len(x)
			dy = [[b[1]*x[n] / (1.0+b[1]*x[n]) for n in range(nb)],
			      [b[0]*x[n] / (1.0+b[1]*x[n]) * (1.0 - b[1]*x[n] / (1.0+b[1]*x[n])) for n in range(nb)]]
			return dy
		
		X = [77.6E0, 114.9E0, 141.1E0, 190.8E0, 239.9E0, 289.0E0, 332.8E0, 378.4E0, 434.8E0, 477.3E0, 536.8E0, 593.1E0, 689.1E0, 760.0E0]
		Y = [10.07E0, 14.73E0, 17.94E0, 23.93E0, 29.61E0, 35.18E0, 40.02E0, 44.82E0, 50.76E0, 55.05E0, 61.01E0, 66.40E0, 75.47E0, 81.78E0]
		
		start = [[500.0, 0.0001], [450.0, 0.0003]]
		par = [4.3736970754E+02, 3.0227324449E-04]
		std_dev = [3.6489174345E+00, 2.9334354479E-06]
		SSR = 5.6419295283E-02
		res_std_dev = 6.8568272111E-02
	
	
	elif name == "Rozman1":
		
		level = "average"
		
		def f(b, x):
			PI = 3.141592653589793238462643383279E0
			nb = len(x)
			y = [b[0] - b[1]*x[n] - math.atan(b[2]/(x[n]-b[3]))/PI for n in range(nb)]
			return y
		
		def df(b, x):
			PI = 3.141592653589793238462643383279E0
			nb = len(x)
			dy = [[1.0 for n in range(nb)],
			      [-x[n] for n in range(nb)],
			      [-1.0 / (PI * (1.0 + (b[2]/(x[n]-b[3]))**2)) / (x[n]-b[3]) for n in range(nb)],
			      [-1.0 / (PI * (1.0 + (b[2]/(x[n]-b[3]))**2)) * b[2]/(x[n]-b[3])**2 for n in range(nb)]]
			return dy
		
		X = [-4868.68, -4868.09, -4867.41, -3375.19, -3373.14, -3372.03, -2473.74, -2472.35, -2469.45, -1894.65, -1893.40, -1497.24, -1495.85, -1493.41, -1208.68, -1206.18, -1206.04, -997.92, -996.61, -996.31, -834.94, -834.66, -710.03, -530.16, -464.17]
		Y = [0.252429, 0.252141, 0.251809, 0.297989, 0.296257, 0.295319, 0.339603, 0.337731, 0.333820, 0.389510, 0.386998, 0.438864, 0.434887, 0.427893, 0.471568, 0.461699, 0.461144, 0.513532, 0.506641, 0.505062, 0.535648, 0.533726, 0.568064, 0.612886, 0.624169]
		
		start = [[0.1, -0.00001, 1000.0, -100.0], [0.2, -0.000005, 1200.0, -150.0]]
		par = [2.0196866396E-01, -6.1953516256E-06, 1.2044556708E+03, -1.8134269537E+02]
		std_dev = [1.9172666023E-02, 3.2058931691E-06, 7.4050983057E+01, 4.9573513849E+01]
		SSR = 4.9484847331E-04
		res_std_dev = 4.8542984060E-03
	
	
	elif name == "ENSO":
		
		level = "average"
		
		def f(b, x):
			nb = len(x)
			y = [b[0] + b[1]*math.cos( 2.0*math.pi*x[n]/12 ) + b[2]*math.sin( 2.0*math.pi*x[n]/12.0 )
                + b[4]*math.cos( 2.0*math.pi*x[n]/b[3] ) + b[5]*math.sin( 2.0*math.pi*x[n]/b[3] )
                + b[7]*math.cos( 2.0*math.pi*x[n]/b[6] ) + b[8]*math.sin( 2.0*math.pi*x[n]/b[6] ) for n in range(nb)]
			return y
 
		
		def df(b, x):
			nb = len(x)
			dy = [[1.0 for n in range(nb)],
			      [math.cos( 2.0*math.pi*x[n]/12.0 ) for n in range(nb)],
			      [math.sin( 2.0*math.pi*x[n]/12.0 ) for n in range(nb)],
			      [(b[4]*math.sin( 2.0*math.pi*x[n]/b[3] ) - b[5]*math.cos( 2.0*math.pi*x[n]/b[3] )) * 2.0*math.pi*x[n]/b[3]**2 for n in range(nb)],
			      [math.cos( 2.0*math.pi*x[n]/b[3] ) for n in range(nb)],
			      [math.sin( 2.0*math.pi*x[n]/b[3] ) for n in range(nb)],
			      [(b[7]*math.sin( 2.0*math.pi*x[n]/b[6] ) - b[8]*math.cos( 2.0*math.pi*x[n]/b[6] )) * 2.0*math.pi*x[n]/b[6]**2 for n in range(nb)],
			      [math.cos( 2.0*math.pi*x[n]/b[6] ) for n in range(nb)],
			      [math.sin( 2.0*math.pi*x[n]/b[6] ) for n in range(nb)]]
			return dy
		
		X = [1.000000, 2.000000, 3.000000, 4.000000, 5.000000, 6.000000, 7.000000, 8.000000, 9.000000, 10.000000, 11.00000, 12.00000, 13.00000, 14.00000, 15.00000, 16.00000, 17.00000, 18.00000, 19.00000, 20.00000, 21.00000, 22.00000, 23.00000, 24.00000, 25.00000, 26.00000, 27.00000, 28.00000, 29.00000, 30.00000, 31.00000, 32.00000, 33.00000, 34.00000, 35.00000, 36.00000, 37.00000, 38.00000, 39.00000, 40.00000, 41.00000, 42.00000, 43.00000, 44.00000, 45.00000, 46.00000, 47.00000, 48.00000, 49.00000, 50.00000, 51.00000, 52.00000, 53.00000, 54.00000, 55.00000, 56.00000, 57.00000, 58.00000, 59.00000, 60.00000, 61.00000, 62.00000, 63.00000, 64.00000, 65.00000, 66.00000, 67.00000, 68.00000, 69.00000, 70.00000, 71.00000, 72.00000, 73.00000, 74.00000, 75.00000, 76.00000, 77.00000, 78.00000, 79.00000, 80.00000, 81.00000, 82.00000, 83.00000, 84.00000, 85.00000, 86.00000, 87.00000, 88.00000, 89.00000, 90.00000, 91.00000, 92.00000, 93.00000, 94.00000, 95.00000, 96.00000, 97.00000, 98.00000, 99.00000, 100.00000, 101.00000, 102.00000, 103.00000, 104.00000, 105.00000, 106.0000, 107.0000, 108.0000, 109.0000, 110.0000, 111.0000, 112.0000, 113.0000, 114.0000, 115.0000, 116.0000, 117.0000, 118.0000, 119.0000, 120.0000, 121.0000, 122.0000, 123.0000, 124.0000, 125.0000, 126.0000, 127.0000, 128.0000, 129.0000, 130.0000, 131.0000, 132.0000, 133.0000, 134.0000, 135.0000, 136.0000, 137.0000, 138.0000, 139.0000, 140.0000, 141.0000, 142.0000, 143.0000, 144.0000, 145.0000, 146.0000, 147.0000, 148.0000, 149.0000, 150.0000, 151.0000, 152.0000, 153.0000, 154.0000, 155.0000, 156.0000, 157.0000, 158.0000, 159.0000, 160.0000, 161.0000, 162.0000, 163.0000, 164.0000, 165.0000, 166.0000, 167.0000, 168.0000]
		Y = [12.90000, 11.30000, 10.60000, 11.20000, 10.90000, 7.500000, 7.700000, 11.70000, 12.90000, 14.30000, 10.90000, 13.70000, 17.10000, 14.00000, 15.30000, 8.500000, 5.700000, 5.500000, 7.600000, 8.600000, 7.300000, 7.600000, 12.70000, 11.00000, 12.70000, 12.90000, 13.00000, 10.90000, 10.400000, 10.200000, 8.000000, 10.90000, 13.60000, 10.500000, 9.200000, 12.40000, 12.70000, 13.30000, 10.100000, 7.800000, 4.800000, 3.000000, 2.500000, 6.300000, 9.700000, 11.60000, 8.600000, 12.40000, 10.500000, 13.30000, 10.400000, 8.100000, 3.700000, 10.70000, 5.100000, 10.400000, 10.90000, 11.70000, 11.40000, 13.70000, 14.10000, 14.00000, 12.50000, 6.300000, 9.600000, 11.70000, 5.000000, 10.80000, 12.70000, 10.80000, 11.80000, 12.60000, 15.70000, 12.60000, 14.80000, 7.800000, 7.100000, 11.20000, 8.100000, 6.400000, 5.200000, 12.00000, 10.200000, 12.70000, 10.200000, 14.70000, 12.20000, 7.100000, 5.700000, 6.700000, 3.900000, 8.500000, 8.300000, 10.80000, 16.70000, 12.60000, 12.50000, 12.50000, 9.800000, 7.200000, 4.100000, 10.60000, 10.100000, 10.100000, 11.90000, 13.60000, 16.30000, 17.60000, 15.50000, 16.00000, 15.20000, 11.20000, 14.30000, 14.50000, 8.500000, 12.00000, 12.70000, 11.30000, 14.50000, 15.10000, 10.400000, 11.50000, 13.40000, 7.500000, 0.6000000, 0.3000000, 5.500000, 5.000000, 4.600000, 8.200000, 9.900000, 9.200000, 12.50000, 10.90000, 9.900000, 8.900000, 7.600000, 9.500000, 8.400000, 10.70000, 13.60000, 13.70000, 13.70000, 16.50000, 16.80000, 17.10000, 15.40000, 9.500000, 6.100000, 10.100000, 9.300000, 5.300000, 11.20000, 16.60000, 15.60000, 12.00000, 11.50000, 8.600000, 13.80000, 8.700000, 8.600000, 8.600000, 8.700000, 12.80000, 13.20000, 14.00000, 13.40000, 14.80000]
		
		start = [[11.0, 3.0, 0.5, 40.0, -0.7, -1.3, 25.0, -0.3, 1.4], [10.0, 3.0, 0.5, 44.0, -1.5, 0.5, 26.0, -0.1, 1.5]]
		par = [1.0510749193E+01, 3.0762128085E+00, 5.3280138227E-01, 4.4311088700E+01, -1.6231428586E+00, 5.2554493756E-01, 2.6887614440E+01, 2.1232288488E-01, 1.4966870418E+00]
		std_dev = [1.7488832467E-01, 2.4310052139E-01, 2.4354686618E-01, 9.4408025976E-01, 2.8078369611E-01, 4.8073701119E-01, 4.1612939130E-01, 5.1460022911E-01, 2.5434468893E-01]
		SSR = 7.8853978668E+02
		res_std_dev = 2.2269642403E+00
	
	
	elif name == "MGH09":
		
		level = "higher"
		
		def f(b, x):
			nb = len(x)
			y = [b[0]*(x[n]*x[n]+x[n]*b[1])/(x[n]*x[n]+x[n]*b[2]+b[3]) for n in range(nb)]
			return y
		
		def df(b, x):
			nb = len(x)
			dy = [[(x[n]*x[n]+x[n]*b[1])/(x[n]*x[n]+x[n]*b[2]+b[3]) for n in range(nb)],
			      [b[0]*(x[n])/(x[n]*x[n]+x[n]*b[2]+b[3]) for n in range(nb)],
			      [-b[0]*(x[n]*x[n]+x[n]*b[1])/(x[n]*x[n]+x[n]*b[2]+b[3])**2*x[n] for n in range(nb)],
			      [-b[0]*(x[n]*x[n]+x[n]*b[1])/(x[n]*x[n]+x[n]*b[2]+b[3])**2 for n in range(nb)]]
			return dy
		
		X = [4.000000E+00, 2.000000E+00, 1.000000E+00, 5.000000E-01, 2.500000E-01, 1.670000E-01, 1.250000E-01, 1.000000E-01, 8.330000E-02, 7.140000E-02, 6.250000E-02]
		Y = [1.957000E-01, 1.947000E-01, 1.735000E-01, 1.600000E-01, 8.440000E-02, 6.270000E-02, 4.560000E-02, 3.420000E-02, 3.230000E-02, 2.350000E-02, 2.460000E-02]
		
		start = [[25, 39, 41.5, 39], [0.25, 0.39, 0.415, 0.39]]
		par = [1.9280693458E-01, 1.9128232873E-01, 1.2305650693E-01, 1.3606233068E-01]
		std_dev = [1.1435312227E-02, 1.9633220911E-01, 8.0842031232E-02, 9.0025542308E-02]
		SSR = 3.0750560385E-04
		res_std_dev = 6.6279236551E-03
	
	
	elif name == "Thurber":
		
		level = "higher"
		
		def f(b, x):
			nb = len(x)
			y = [(b[0] + b[1]*x[n] + b[2]*x[n]*x[n] + b[3]*x[n]*x[n]*x[n]) / (1.0 + b[4]*x[n] + b[5]*x[n]*x[n] + b[6]*x[n]*x[n]*x[n]) for n in range(nb)]
			return y
		
		def df(b, x):
			nb = len(x)
			dy = [[(1.0) / (1.0 + b[4]*x[n] + b[5]*x[n]*x[n] + b[6]*x[n]*x[n]*x[n]) for n in range(nb)],
			      [(x[n]) / (1.0 + b[4]*x[n] + b[5]*x[n]*x[n] + b[6]*x[n]*x[n]*x[n]) for n in range(nb)],
			      [(x[n]*x[n]) / (1.0 + b[4]*x[n] + b[5]*x[n]*x[n] + b[6]*x[n]*x[n]*x[n]) for n in range(nb)],
			      [(x[n]*x[n]*x[n]) / (1.0 + b[4]*x[n] + b[5]*x[n]*x[n] + b[6]*x[n]*x[n]*x[n]) for n in range(nb)],
			      [-(b[0] + b[1]*x[n] + b[2]*x[n]*x[n] + b[3]*x[n]*x[n]*x[n]) / (1.0 + b[4]*x[n] + b[5]*x[n]*x[n] + b[6]*x[n]*x[n]*x[n])**2 * x[n] for n in range(nb)],
			      [-(b[0] + b[1]*x[n] + b[2]*x[n]*x[n] + b[3]*x[n]*x[n]*x[n]) / (1.0 + b[4]*x[n] + b[5]*x[n]*x[n] + b[6]*x[n]*x[n]*x[n])**2 * x[n]*x[n] for n in range(nb)],
			      [-(b[0] + b[1]*x[n] + b[2]*x[n]*x[n] + b[3]*x[n]*x[n]*x[n]) / (1.0 + b[4]*x[n] + b[5]*x[n]*x[n] + b[6]*x[n]*x[n]*x[n])**2 * x[n]*x[n]*x[n] for n in range(nb)]]
			return dy
		
		X = [-3.067E0, -2.981E0, -2.921E0, -2.912E0, -2.840E0, -2.797E0, -2.702E0, -2.699E0, -2.633E0, -2.481E0, -2.363E0, -2.322E0, -1.501E0, -1.460E0, -1.274E0, -1.212E0, -1.100E0, -1.046E0, -0.915E0, -0.714E0, -0.566E0, -0.545E0, -0.400E0, -0.309E0, -0.109E0, -0.103E0, 0.010E0, 0.119E0, 0.377E0, 0.790E0, 0.963E0, 1.006E0, 1.115E0, 1.572E0, 1.841E0, 2.047E0,  2.200E0]
		Y = [80.574E0, 84.248E0, 87.264E0, 87.195E0, 89.076E0, 89.608E0, 89.868E0, 90.101E0, 92.405E0, 95.854E0, 100.696E0, 101.060E0, 401.672E0, 390.724E0, 567.534E0, 635.316E0, 733.054E0, 759.087E0, 894.206E0, 990.785E0, 1090.109E0, 1080.914E0, 1122.643E0, 1178.351E0, 1260.531E0, 1273.514E0, 1288.339E0, 1327.543E0, 1353.863E0, 1414.509E0, 1425.208E0, 1421.384E0, 1442.962E0, 1464.350E0, 1468.705E0, 1447.894E0, 1457.628E0]
		
		start = [[1000, 1000, 400, 40, 0.7, 0.3, 0.03], [1300, 1500, 500, 75, 1, 0.4, 0.05]]
		par = [1.2881396800E+03, 1.4910792535E+03, 5.8323836877E+02, 7.5416644291E+01, 9.6629502864E-01, 3.9797285797E-01, 4.9727297349E-02]
		std_dev = [4.6647963344E+00, 3.9571156086E+01, 2.8698696102E+01, 5.5675370270E+00, 3.1333340687E-02, 1.4984928198E-02, 6.5842344623E-03]
		SSR = 5.6427082397E+03
		res_std_dev = 1.3714600784E+01
	
	
	elif name == "BoxBOD":
		
		level = "higher"
		
		def f(b, x):
			nb = len(x)
			y = [b[0]*(1.0-math.exp(-b[1]*x[n])) for n in range(nb)]
			return y
		
		def df(b, x):
			nb = len(x)
			dy = [[(1.0-math.exp(-b[1]*x[n])) for n in range(nb)],
			      [b[0]*x[n]*math.exp(-b[1]*x[n]) for n in range(nb)]]
			return dy
		
		X = [1, 2, 3, 5, 7, 10]
		Y = [109, 149, 149, 191, 213, 224]
		
		start = [[1, 1], [100, 0.75]]
		par = [2.1380940889E+02, 5.4723748542E-01]
		std_dev = [1.2354515176E+01, 1.0455993237E-01]
		SSR = 1.1680088766E+03
		res_std_dev = 1.7088072423E+01
	
	
	elif name == "Rat42":
		
		level = "higher"
		
		def f(b, x):
			nb = len(x)
			y = [b[0] / (1.0+math.exp(b[1]-b[2]*x[n])) for n in range(nb)]
			return y
		
		def df(b, x):
			nb = len(x)
			dy = [[1.0 / (1.0+math.exp(b[1]-b[2]*x[n])) for n in range(nb)],
			      [-b[0] / (1.0+math.exp(b[1]-b[2]*x[n]))**2 * math.exp(b[1]-b[2]*x[n]) for n in range(nb)],
			      [b[0] / (1.0+math.exp(b[1]-b[2]*x[n]))**2 * math.exp(b[1]-b[2]*x[n]) * x[n] for n in range(nb)]]
			return dy
		
		X = [9.000E0, 14.000E0, 21.000E0, 28.000E0, 42.000E0, 57.000E0, 63.000E0, 70.000E0, 79.000E0]
		Y = [8.930E0, 10.800E0, 18.590E0, 22.330E0, 39.350E0, 56.110E0, 61.730E0, 64.620E0, 67.080E0]
		
		start = [[100, 1.0, 0.1], [75.0, 2.5, 0.07]]
		par = [7.2462237576E+01, 2.6180768402E+00, 6.7359200066E-02]
		std_dev = [1.7340283401E+00, 8.8295217536E-02, 3.4465663377E-03]
		SSR = 8.0565229338E+00
		res_std_dev = 1.1587725499E+00
	
	
	elif name == "MGH10":
		
		level = "higher"
		
		def f(b, x):
			nb = len(x)
			y = [b[0] * math.exp(b[1]/(x[n]+b[2])) for n in range(nb)]
			return y
		
		def df(b, x):
			nb = len(x)
			dy = [[math.exp(b[1]/(x[n]+b[2])) for n in range(nb)],
			      [b[0] * math.exp(b[1]/(x[n]+b[2])) / (x[n]+b[2]) for n in range(nb)],
			      [b[0] * math.exp(b[1]/(x[n]+b[2])) * -b[1]/(x[n]+b[2])**2 for n in range(nb)]]
			return dy
		
		X = [5.000000E+01, 5.500000E+01, 6.000000E+01, 6.500000E+01, 7.000000E+01, 7.500000E+01, 8.000000E+01, 8.500000E+01, 9.000000E+01, 9.500000E+01, 1.000000E+02, 1.050000E+02, 1.100000E+02, 1.150000E+02, 1.200000E+02, 1.250000E+02]
		Y = [3.478000E+04, 2.861000E+04, 2.365000E+04, 1.963000E+04, 1.637000E+04, 1.372000E+04, 1.154000E+04, 9.744000E+03, 8.261000E+03, 7.030000E+03, 6.005000E+03, 5.147000E+03, 4.427000E+03, 3.820000E+03, 3.307000E+03, 2.872000E+03]
		
		start = [[2.0, 400000.0, 25000.0], [0.02, 4000.0, 250.0]]
		par = [5.6096364710E-03, 6.1813463463E+03, 3.4522363462E+02]
		std_dev = [1.5687892471E-04, 2.3309021107E+01, 7.8486103508E-01]
		SSR = 8.7945855171E+01
		res_std_dev = 2.6009740065E+00
	
	
	elif name == "Eckerle4":
		
		level = "higher"
		
		def f(b, x):
			nb = len(x)
			y = [(b[0]/b[1]) * math.exp(-0.5*((x[n]-b[2])/b[1])**2) for n in range(nb)]
			return y
		
		def df(b, x):
			nb = len(x)
			dy = [[(1.0/b[1]) * math.exp(-0.5*((x[n]-b[2])/b[1])**2) for n in range(nb)],
			      [(-b[0]/b[1]**2 + (b[0]/b[1]) * (x[n]-b[2])**2/b[1]**3) * math.exp(-0.5*((x[n]-b[2])/b[1])**2) for n in range(nb)],
			      [(b[0]/b[1]) * math.exp(-0.5*((x[n]-b[2])/b[1])**2) * (x[n]-b[2])/b[1]**2 for n in range(nb)]]
			return dy
		
		X = [400.000000E0, 405.000000E0, 410.000000E0, 415.000000E0, 420.000000E0, 425.000000E0, 430.000000E0, 435.000000E0, 436.500000E0, 438.000000E0, 439.500000E0, 441.000000E0, 442.500000E0, 444.000000E0, 445.500000E0, 447.000000E0, 448.500000E0, 450.000000E0, 451.500000E0, 453.000000E0, 454.500000E0, 456.000000E0, 457.500000E0, 459.000000E0, 460.500000E0, 462.000000E0, 463.500000E0, 465.000000E0, 470.000000E0, 475.000000E0, 480.000000E0, 485.000000E0, 490.000000E0, 495.000000E0, 500.000000E0]
		Y = [0.0001575E0, 0.0001699E0, 0.0002350E0, 0.0003102E0, 0.0004917E0, 0.0008710E0, 0.0017418E0, 0.0046400E0, 0.0065895E0, 0.0097302E0, 0.0149002E0, 0.0237310E0, 0.0401683E0, 0.0712559E0, 0.1264458E0, 0.2073413E0, 0.2902366E0, 0.3445623E0, 0.3698049E0, 0.3668534E0, 0.3106727E0, 0.2078154E0, 0.1164354E0, 0.0616764E0, 0.0337200E0, 0.0194023E0, 0.0117831E0, 0.0074357E0, 0.0022732E0, 0.0008800E0, 0.0004579E0, 0.0002345E0, 0.0001586E0, 0.0001143E0, 0.0000710E0]
		
		start = [[1.0, 10.0, 500.0], [1.5, 5.0, 450.0]]
		par = [1.5543827178E+00, 4.0888321754E+00, 4.5154121844E+02]
		std_dev = [1.5408051163E-02, 4.6803020753E-02, 4.6800518816E-02]
		SSR = 1.4635887487E-03
		res_std_dev = 6.7629245447E-03
	
	
	elif name == "Rat43":
		
		level = "higher"
		
		def f(b, x):
			nb = len(x)
			y = [b[0] / ((1.0+math.exp(b[1]-b[2]*x[n]))**(1.0/b[3])) for n in range(nb)]
			return y
		
		def df(b, x):
			nb = len(x)
			dy = [[1.0 / ((1.0+math.exp(b[1]-b[2]*x[n]))**(1.0/b[3])) for n in range(nb)],
			      [-(1.0/b[3]) * b[0] / ((1.0+math.exp(b[1]-b[2]*x[n]))**(1.0/b[3]+1.0)) * math.exp(b[1]-b[2]*x[n]) for n in range(nb)],
			      [(1.0/b[3]) * b[0] / ((1.0+math.exp(b[1]-b[2]*x[n]))**(1.0/b[3]+1.0)) * math.exp(b[1]-b[2]*x[n]) * x[n] for n in range(nb)],
			      [math.log(1.0+math.exp(b[1]-b[2]*x[n])) * b[0] / ((1.0+math.exp(b[1]-b[2]*x[n]))**(1.0/b[3])) * (1.0/b[3]**2) for n in range(nb)]]
			return dy
		
		X = [1.0E0, 2.0E0, 3.0E0, 4.0E0, 5.0E0, 6.0E0, 7.0E0, 8.0E0, 9.0E0, 10.0E0, 11.0E0, 12.0E0, 13.0E0, 14.0E0, 15.0E0]
		Y = [16.08E0, 33.83E0, 65.80E0, 97.20E0, 191.55E0, 326.20E0, 386.87E0, 520.53E0, 590.03E0, 651.92E0, 724.93E0, 699.56E0, 689.96E0, 637.56E0, 717.41E0]
		
		start = [[100.0, 10.0, 1.0, 1.0], [700.0, 5.0, 0.75, 1.3]]
		par = [6.9964151270E+02, 5.2771253025E+00, 7.5962938329E-01, 1.2792483859E+00]
		std_dev = [1.6302297817E+01, 2.0828735829E+00, 1.9566123451E-01, 6.8761936385E-01]
		SSR = 8.7864049080E+03
		res_std_dev = 2.8262414662E+01
	
	
	elif name == "Bennett5":
		
		level = "higher"
		
		def f(b, x):
			nb = len(x)
			y = [b[0] * (b[1]+x[n])**(-1.0/b[2]) for n in range(nb)]
			return y
		
		def df(b, x):
			nb = len(x)
			dy = [[(b[1]+x[n])**(-1.0/b[2]) for n in range(nb)],
			      [b[0] * (-1.0/b[2]) * (b[1]+x[n])**(-1.0/b[2]-1.0) for n in range(nb)],
			      [b[0] * math.log(b[1]+x[n]) * (b[1]+x[n])**(-1.0/b[2]) * (1.0/b[2]**2) for n in range(nb)]]
			return dy
		
		X = [7.447168E0, 8.102586E0, 8.452547E0, 8.711278E0, 8.916774E0, 9.087155E0, 9.232590E0, 9.359535E0, 9.472166E0, 9.573384E0, 9.665293E0, 9.749461E0, 9.827092E0, 9.899128E0, 9.966321E0, 10.029280E0, 10.088510E0, 10.144430E0, 10.197380E0, 10.247670E0, 10.295560E0, 10.341250E0, 10.384950E0, 10.426820E0, 10.467000E0, 10.505640E0, 10.542830E0, 10.578690E0, 10.613310E0, 10.646780E0, 10.679150E0, 10.710520E0, 10.740920E0, 10.770440E0, 10.799100E0, 10.826970E0, 10.854080E0, 10.880470E0, 10.906190E0, 10.931260E0, 10.955720E0, 10.979590E0, 11.002910E0, 11.025700E0, 11.047980E0, 11.069770E0, 11.091100E0, 11.111980E0, 11.132440E0, 11.152480E0, 11.172130E0, 11.191410E0, 11.210310E0, 11.228870E0, 11.247090E0, 11.264980E0, 11.282560E0, 11.299840E0, 11.316820E0, 11.333520E0, 11.349940E0, 11.366100E0, 11.382000E0, 11.397660E0, 11.413070E0, 11.428240E0, 11.443200E0, 11.457930E0, 11.472440E0, 11.486750E0, 11.500860E0, 11.514770E0, 11.528490E0, 11.542020E0, 11.555380E0, 11.568550E0, 11.581560E0, 11.594420E0, 11.607121E0, 11.619640E0, 11.632000E0, 11.644210E0, 11.656280E0, 11.668200E0, 11.679980E0, 11.691620E0, 11.703130E0, 11.714510E0, 11.725760E0, 11.736880E0, 11.747890E0, 11.758780E0, 11.769550E0, 11.780200E0, 11.790730E0, 11.801160E0, 11.811480E0, 11.821700E0, 11.831810E0, 11.841820E0, 11.851730E0, 11.861550E0, 11.871270E0, 11.880890E0, 11.890420E0, 11.899870E0, 11.909220E0, 11.918490E0, 11.927680E0, 11.936780E0, 11.945790E0, 11.954730E0, 11.963590E0, 11.972370E0, 11.981070E0, 11.989700E0, 11.998260E0, 12.006740E0, 12.015150E0, 12.023490E0, 12.031760E0, 12.039970E0, 12.048100E0, 12.056170E0, 12.064180E0, 12.072120E0, 12.080010E0, 12.087820E0, 12.095580E0, 12.103280E0, 12.110920E0, 12.118500E0, 12.126030E0, 12.133500E0, 12.140910E0, 12.148270E0, 12.155570E0, 12.162830E0, 12.170030E0, 12.177170E0, 12.184270E0, 12.191320E0, 12.198320E0, 12.205270E0, 12.212170E0, 12.219030E0, 12.225840E0, 12.232600E0, 12.239320E0, 12.245990E0, 12.252620E0, 12.259200E0, 12.265750E0, 12.272240E0]
		Y = [-34.834702E0, -34.393200E0, -34.152901E0, -33.979099E0, -33.845901E0, -33.732899E0, -33.640301E0, -33.559200E0, -33.486801E0, -33.423100E0, -33.365101E0, -33.313000E0, -33.260899E0, -33.217400E0, -33.176899E0, -33.139198E0, -33.101601E0, -33.066799E0, -33.035000E0, -33.003101E0, -32.971298E0, -32.942299E0, -32.916302E0, -32.890202E0, -32.864101E0, -32.841000E0, -32.817799E0, -32.797501E0, -32.774300E0, -32.757000E0, -32.733799E0, -32.716400E0, -32.699100E0, -32.678799E0, -32.661400E0, -32.644001E0, -32.626701E0, -32.612202E0, -32.597698E0, -32.583199E0, -32.568699E0, -32.554298E0, -32.539799E0, -32.525299E0, -32.510799E0, -32.499199E0, -32.487598E0, -32.473202E0, -32.461601E0, -32.435501E0, -32.435501E0, -32.426800E0, -32.412300E0, -32.400799E0, -32.392101E0, -32.380501E0, -32.366001E0, -32.357300E0, -32.348598E0, -32.339901E0, -32.328400E0, -32.319698E0, -32.311001E0, -32.299400E0, -32.290699E0, -32.282001E0, -32.273300E0, -32.264599E0, -32.256001E0, -32.247299E0, -32.238602E0, -32.229900E0, -32.224098E0, -32.215401E0, -32.203800E0, -32.198002E0, -32.189400E0, -32.183601E0, -32.174900E0, -32.169102E0, -32.163300E0, -32.154598E0, -32.145901E0, -32.140099E0, -32.131401E0, -32.125599E0, -32.119801E0, -32.111198E0, -32.105400E0, -32.096699E0, -32.090900E0, -32.088001E0, -32.079300E0, -32.073502E0, -32.067699E0, -32.061901E0, -32.056099E0, -32.050301E0, -32.044498E0, -32.038799E0, -32.033001E0, -32.027199E0, -32.024300E0, -32.018501E0, -32.012699E0, -32.004002E0, -32.001099E0, -31.995300E0, -31.989500E0, -31.983700E0, -31.977900E0, -31.972099E0, -31.969299E0, -31.963501E0, -31.957701E0, -31.951900E0, -31.946100E0, -31.940300E0, -31.937401E0, -31.931601E0, -31.925800E0, -31.922899E0, -31.917101E0, -31.911301E0, -31.908400E0, -31.902599E0, -31.896900E0, -31.893999E0, -31.888201E0, -31.885300E0, -31.882401E0, -31.876600E0, -31.873699E0, -31.867901E0, -31.862101E0, -31.859200E0, -31.856300E0, -31.850500E0, -31.844700E0, -31.841801E0, -31.838900E0, -31.833099E0, -31.830200E0, -31.827299E0, -31.821600E0, -31.818701E0, -31.812901E0, -31.809999E0, -31.807100E0, -31.801300E0, -31.798401E0, -31.795500E0, -31.789700E0, -31.786800E0]
		
		start = [[-2000.0, 50.0, 0.8], [-1500.0, 45.0, 0.85]]
		par = [-2.5235058043E+03, 4.6736564644E+01 , 9.3218483193E-01]
		std_dev = [2.9715175411E+02, 1.2448871856E+00, 2.0272299378E-02]
		SSR = 5.2404744073E-04
		res_std_dev = 1.8629312528E-03
	
	
	return f, df, X, Y, par, start, SSR, level
