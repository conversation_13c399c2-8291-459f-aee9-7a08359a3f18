# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## 项目概述

OpenFilters是一个用于光学滤光片设计的Python应用程序，版本1.1.1。该项目使用wxPython构建GUI界面，包含用于光学计算的C++扩展模块。

## 架构结构

### 核心模块
- **Filters.py**: 主程序入口点，启动GUI界面
- **optical_filter.py**: 核心光学滤光片类，处理滤光片的数据结构和文件I/O
- **materials.py**: 材料管理和光学常数处理
- **targets.py**: 目标函数定义和优化目标
- **project.py**: 项目管理和状态保存

### GUI模块 (GUI/)
完整的wxPython图形界面实现：
- **GUI_main_window.py**: 主窗口和应用程序框架
- **GUI_stack.py**: 薄膜堆栈编辑界面
- **GUI_materials.py**: 材料数据库管理
- **GUI_optimization.py**: 优化算法控制界面
- **GUI_plot.py**: 光谱绘图和可视化
- **GUI_targets.py**: 目标设置界面
- **GUI_color.py**: 颜色计算和显示

### 光学计算核心 (abeles/)
基于Abeles矩阵方法的光学计算库：
- **r_and_t.py**: 反射率和透射率计算
- **admittance.py**: 光学导纳计算
- **electric_field.py**: 电场分布计算
- **phase.py**: 相位计算
- **derivatives.py**: 导数计算（用于优化）
- **dispersion.py**: 色散关系处理
- **_abeles/**: C++扩展模块源码

### 数学工具 (moremath/)
数值计算辅助库：
- **Levenberg_Marquardt.py**: LM优化算法
- **interpolation.py**: 插值算法
- **linear_algebra.py**: 线性代数运算
- **_moremath/**: C++扩展模块源码

### 优化算法
- **optimization_refinement.py**: 精细优化算法
- **optimization_needles.py**: 针式优化方法
- **optimization_Fourier.py**: 傅里叶变换优化
- **optimization_steps.py**: 步进优化算法

### 配置系统 (config/)
模块化配置管理，每个功能模块都有对应的配置文件

## 常用开发命令

### 构建命令
```bash
# 编译C++扩展库
make libraries

# 单独编译abeles库
make abeles

# 单独编译moremath库  
make moremath

# 创建可执行文件 (Windows: exe, Mac: app)
make executable

# 创建安装程序
make installer

# 创建完整发布包
make distribution
```

### 运行和测试
```bash
# 直接运行OpenFilters
python Filters.py

# 运行测试套件
python tests.py

# 运行abeles模块测试
cd abeles && python tests.py

# 运行moremath模块测试  
cd moremath && python tests.py
```

### 清理命令
```bash
# 清理构建文件（保留库和发布文件）
make clean

# 完全清理所有生成文件
make cleanall
```

## 开发注意事项

### C++扩展模块
- 两个关键的C++扩展：`abeles/_abeles.pyd` 和 `moremath/_moremath.pyd`
- 修改C++代码后需要重新编译：`make libraries`
- Windows需要合适的编译器环境

### Python版本兼容性
- 代码中包含Python 2/3兼容性处理
- 使用`from __future__ import division`确保除法行为一致
- 导入语句处理了Python版本差异

### 材料和数据文件
- **materials/**: 材料光学常数文件(.mat)
- **color/illuminants/**: CIE标准光源数据
- **color/observers/**: CIE标准观察者数据
- **examples/**: 示例项目文件(.ofp)

### 项目文件格式
项目使用.ofp格式保存，包含滤光片设计的所有参数和设置

### 性能关键组件
- 光学计算密集部分使用C++实现以提高性能
- 优化算法需要大量矩阵运算，依赖编译后的扩展模块