# config_preproduction.py
# 
# Configurations related to preproduction analysis for the Filters
# software.
# 
# Copyright (c) 2007 <PERSON><PERSON>.
# 
# This file is part of OpenFilters.
# 
# OpenFilters is free software; you can redistribute it and/or modify
# it under the terms of the GNU General Public License as published by
# the Free Software Foundation; either version 2 of the License, or (at
# your option) any later version.
#
# OpenFilters is distributed in the hope that it will be useful, but
# WITHOUT ANY WARRANTY; without even the implied warranty of
# MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
# General Public License for more details.
#
# You should have received a copy of the GNU General Public License
# along with this program; if not, write to the Free Software
# Foundation, Inc., 59 Temple Place, Suite 330, Boston, MA 02111-1307
# USA


# Default parameters for random error analysis.
THICKNESS_ERROR_TYPE = 0
RELATIVE_THICKNESS_ERROR = 0.01
PHYSICAL_THICKNESS_ERROR = 1.0
DISTRIBUTION = 0
NB_TESTS = 100
