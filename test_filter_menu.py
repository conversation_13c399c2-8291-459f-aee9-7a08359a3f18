#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
测试Filter菜单功能的完整工作流程
"""

import sys
import wx
import traceback

def test_filter_workflow():
    """测试完整的Filter工作流程"""
    print("=== 测试Filter菜单完整工作流程 ===")
    print()
    
    try:
        # 导入必要模块
        import GUI
        import optical_filter
        import project
        
        # 创建应用程序
        app = GUI.Filters_GUI(0)
        main_window = app.GetTopWindow()
        print("[OK] 应用程序创建成功")
        
        # 步骤1：创建新项目
        print("\n步骤1：创建新项目")
        try:
            # 创建项目对象
            main_window.project = project.project()
            main_window.project_filename = None
            print("[OK] 项目对象创建成功")
        except Exception as e:
            print("[FAIL] 项目创建失败:", e)
            return False
        
        # 步骤2：添加滤光片
        print("\n步骤2：添加滤光片")
        try:
            # 创建滤光片
            filter_obj = optical_filter.optical_filter()
            filter_nb = main_window.project.add_filter(filter_obj)
            main_window.selected_filter_nb = filter_nb
            
            # 初始化数据结构
            if not hasattr(main_window, 'data'):
                main_window.data = []
            while len(main_window.data) <= filter_nb:
                main_window.data.append([[] for _ in range(10)])  # 假设有10个页面
                
            print(f"[OK] 滤光片添加成功，编号: {filter_nb}")
            print(f"     项目滤光片总数: {main_window.project.get_nb_filters()}")
            print(f"     当前选中滤光片: {main_window.selected_filter_nb}")
        except Exception as e:
            print("[FAIL] 滤光片添加失败:", e)
            traceback.print_exc()
            return False
        
        # 步骤3：更新菜单状态
        print("\n步骤3：更新菜单状态")
        try:
            # 设置必要的属性
            if not hasattr(main_window, 'modifying'):
                main_window.modifying = False
            if not hasattr(main_window, 'calculating'):
                main_window.calculating = False
            if not hasattr(main_window, 'lower_notebook'):
                # 创建模拟notebook
                main_window.lower_notebook = wx.Notebook(main_window)
                main_window.lower_notebook.AddPage(wx.Panel(main_window.lower_notebook), "Test")
            
            # 调用菜单更新
            main_window.update_menu()
            print("[OK] 菜单更新成功")
        except Exception as e:
            print("[FAIL] 菜单更新失败:", e)
            traceback.print_exc()
            return False
        
        # 步骤4：测试Filter菜单功能
        print("\n步骤4：测试Filter菜单功能")
        
        # 检查菜单是否启用
        filter_menu = main_window.main_menu.GetMenu(2)  # Filter菜单
        properties_enabled = main_window.main_menu.IsEnabled(main_window.filter_properties_ID)
        add_layer_enabled = main_window.main_menu.IsEnabled(main_window.add_layer_ID)
        
        print(f"     Properties菜单启用: {properties_enabled}")
        print(f"     Add layer菜单启用: {add_layer_enabled}")
        
        # 测试Properties对话框
        if properties_enabled:
            try:
                selected_filter = main_window.get_selected_filter(show_warning=False)
                if selected_filter:
                    print("[OK] 成功获取选中的滤光片")
                    
                    # 测试对话框创建（不显示）
                    from GUI.GUI_filter_properties import filter_property_dialog
                    dialog = filter_property_dialog(main_window, selected_filter)
                    print("[OK] Properties对话框创建成功")
                    dialog.Destroy()
                else:
                    print("[FAIL] 无法获取选中的滤光片")
            except Exception as e:
                print("[FAIL] Properties对话框测试失败:", e)
                traceback.print_exc()
        
        # 测试Add Layer功能
        if add_layer_enabled:
            try:
                # 测试添加层对话框导入
                from GUI.GUI_layer_dialogs import simple_layer_dialog
                print("[OK] 层对话框模块导入成功")
            except Exception as e:
                print("[FAIL] 层对话框模块导入失败:", e)
        
        app.Destroy()
        return True
        
    except Exception as e:
        print("[FAIL] 测试过程中发生错误:", e)
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_filter_workflow()
    print()
    if success:
        print("=== Filter菜单测试成功！===")
        print("所有主要功能都可以正常工作。")
        print()
        print("使用说明：")
        print("1. 启动OpenFilters后，先创建新项目")
        print("2. 添加一个滤光片")
        print("3. 选中滤光片后，Filter菜单功能将变为可用")
        print("4. 现在可以使用Properties、Add layer等功能")
    else:
        print("=== Filter菜单测试失败 ===")
        print("请检查错误信息并进行修复。")