import wx
import time

from .GUI_targets import reflection_target_dialog, transmission_target_dialog, absorption_target_dialog, \
    reflection_spectrum_target_dialog, transmission_spectrum_target_dialog, absorption_spectrum_target_dialog
from .GUI_layer_dialogs import simple_layer_dialog, import_layer_dialog
from .GUI_optimization import optimization_refinement_dialog, optimization_needles_dialog, optimization_steps_dialog


class TestCase(object):
    def __init__(self, name, action, requires=None):
        self.name = name
        self.action = action
        self.requires = requires or set()


class menu_selftest_dialog(wx.Dialog):
    def __init__(self, parent):
        wx.Dialog.__init__(self, parent, -1, "Menu self-test", style=wx.CAPTION|wx.RESIZE_BORDER)
        self.parent = parent

        self.log_ctrl = wx.TextCtrl(self, -1, "", style=wx.TE_MULTILINE|wx.TE_READONLY|wx.HSCROLL, size=(700, 400))
        self.run_btn = wx.<PERSON><PERSON>(self, -1, "Run")
        self.save_btn = wx.<PERSON><PERSON>(self, -1, "Save log")
        self.close_btn = wx.But<PERSON>(self, wx.ID_CLOSE)

        btns = wx.BoxSizer(wx.HORIZONTAL)
        btns.Add(self.run_btn, 0)
        btns.Add(self.save_btn, 0, wx.LEFT, 10)
        btns.Add(self.close_btn, 0, wx.LEFT, 10)

        sizer = wx.BoxSizer(wx.VERTICAL)
        sizer.Add(self.log_ctrl, 1, wx.EXPAND|wx.ALL, 10)
        sizer.Add(btns, 0, wx.ALIGN_RIGHT|wx.ALL, 10)
        self.SetSizerAndFit(sizer)
        self.SetSize((760, 520))

        self.Bind(wx.EVT_BUTTON, self.run_all, self.run_btn)
        self.Bind(wx.EVT_BUTTON, self.save_log, self.save_btn)
        self.Bind(wx.EVT_BUTTON, lambda evt: self.EndModal(wx.ID_OK), self.close_btn)

        self.tests = self._build_tests()

    def log(self, msg):
        ts = time.strftime("%H:%M:%S")
        line = f"[{ts}] {msg}\n"
        self.log_ctrl.AppendText(line)
        try:
            print(line.strip())
        except Exception:
            pass

    def save_log(self, event=None):
        try:
            with open("diagnostics_menu_selftest.log", "w", encoding="utf-8") as f:
                f.write(self.log_ctrl.GetValue())
            self.log("Saved log to diagnostics_menu_selftest.log")
        except Exception as e:
            wx.MessageBox("Failed to save log: %s" % str(e), "Error", wx.ICON_ERROR|wx.OK)

    def _build_tests(self):
        tests = []
        # Project → Add Target (discrete)
        tests.append(TestCase("Project/Add Target/Reflection", lambda: self._show_dialog(reflection_target_dialog(self.parent))))
        tests.append(TestCase("Project/Add Target/Transmission", lambda: self._show_dialog(transmission_target_dialog(self.parent))))
        tests.append(TestCase("Project/Add Target/Absorption", lambda: self._show_dialog(absorption_target_dialog(self.parent))))
        # Project → Add Target (spectrum)
        tests.append(TestCase("Project/Add Target/Reflection Spectrum", lambda: self._show_dialog(reflection_spectrum_target_dialog(self.parent))))
        tests.append(TestCase("Project/Add Target/Transmission Spectrum", lambda: self._show_dialog(transmission_spectrum_target_dialog(self.parent))))
        tests.append(TestCase("Project/Add Target/Absorption Spectrum", lambda: self._show_dialog(absorption_spectrum_target_dialog(self.parent))))
        # Filter dialogs (require filter context)
        tests.append(TestCase("Filter/Add layer", self._test_add_layer, requires={"filter"}))
        tests.append(TestCase("Filter/Import layer", self._test_import_layer, requires={"filter"}))
        # Design/Optimize (require filter and targets)
        tests.append(TestCase("Design/Refine", self._test_refine, requires={"filter","target"}))
        tests.append(TestCase("Design/Needles", self._test_needles, requires={"filter","target"}))
        tests.append(TestCase("Design/Steps", self._test_steps, requires={"filter","target"}))
        return tests

    def run_all(self, event=None):
        self.log_ctrl.SetValue("")
        self.log("Starting menu self-test")
        self._prepare_context()
        passed = failed = 0
        for case in self.tests:
            try:
                self._ensure_requirements(case.requires)
                case.action()
                self.log(f"{case.name}: PASS")
                passed += 1
            except Exception as e:
                self.log(f"{case.name}: FAIL - {e}")
                failed += 1
        self.log(f"Completed. Passed={passed}, Failed={failed}")

    def _prepare_context(self):
        # Ensure project exists
        if not hasattr(self.parent, "project") or self.parent.project is None:
            try:
                self.parent.on_new_project(None)
            except Exception:
                pass

    def _ensure_requirements(self, reqs):
        reqs = reqs or set()
        if "filter" in reqs:
            if getattr(self.parent, "selected_filter_nb", -1) < 0:
                # Try to create a minimal filter through existing GUI method
                try:
                    self.parent.on_new_filter()
                except Exception:
                    # Fallback: show properties dialog which may create a default filter
                    self.parent.on_filter_properties()
        if "target" in reqs:
            if getattr(self.parent, "selected_target_nb", -1) < 0:
                # Open reflection target dialog and cancel immediately after set defaults
                self._show_dialog(reflection_target_dialog(self.parent))

    def _show_dialog(self, dlg):
        wx.CallLater(120, dlg.EndModal, wx.ID_CANCEL)
        dlg.ShowModal()
        dlg.Destroy()

    # Specific tests using existing dialogs
    def _test_add_layer(self):
        dlg = simple_layer_dialog(self.parent, self.parent.project.get_filter(self.parent.selected_filter_nb))
        self._show_dialog(dlg)

    def _test_import_layer(self):
        dlg = import_layer_dialog(self.parent, self.parent.project.get_filter(self.parent.selected_filter_nb))
        self._show_dialog(dlg)

    def _test_refine(self):
        flt = self.parent.project.get_filter(self.parent.selected_filter_nb)
        tgts = self.parent.project.get_targets()
        dlg = optimization_refinement_dialog(self.parent, flt, tgts)
        self._show_dialog(dlg)

    def _test_needles(self):
        flt = self.parent.project.get_filter(self.parent.selected_filter_nb)
        tgts = self.parent.project.get_targets()
        dlg = optimization_needles_dialog(self.parent, flt, tgts)
        self._show_dialog(dlg)

    def _test_steps(self):
        flt = self.parent.project.get_filter(self.parent.selected_filter_nb)
        tgts = self.parent.project.get_targets()
        dlg = optimization_steps_dialog(self.parent, flt, tgts)
        self._show_dialog(dlg)


