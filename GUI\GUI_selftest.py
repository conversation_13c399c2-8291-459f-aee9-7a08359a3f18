import wx
import time

from .GUI_targets import (
    reflection_target_dialog, transmission_target_dialog, absorption_target_dialog,
    reflection_spectrum_target_dialog, transmission_spectrum_target_dialog, absorption_spectrum_target_dialog,
    reflection_phase_target_dialog, transmission_phase_target_dialog,
    reflection_GD_target_dialog, transmission_GD_target_dialog,
    reflection_GDD_target_dialog, transmission_GDD_target_dialog,
    reflection_color_target_dialog, transmission_color_target_dialog,
    reflection_phase_spectrum_target_dialog, transmission_phase_spectrum_target_dialog,
    reflection_GD_spectrum_target_dialog, transmission_GD_spectrum_target_dialog,
    reflection_GDD_spectrum_target_dialog, transmission_GDD_spectrum_target_dialog
)
from .GUI_layer_dialogs import simple_layer_dialog, import_layer_dialog
from .GUI_optimization import optimization_refinement_dialog, optimization_needles_dialog, optimization_steps_dialog
from .GUI_stack import stack_dialog
from .GUI_filter_properties import filter_property_dialog
from .GUI_materials import manage_materials_dialog, new_material_dialog, import_material_dialog
from .GUI_preproduction import random_errors_dialog
from .GUI_calculate import (
    calculate_reflection_dialog,
    calculate_transmission_dialog,
    calculate_absorption_dialog,
    calculate_reflection_phase_dialog,
    calculate_transmission_phase_dialog,
    calculate_reflection_GD_dialog,
    calculate_transmission_GD_dialog,
    calculate_reflection_GDD_dialog,
    calculate_transmission_GDD_dialog,
    calculate_color_dialog,
    calculate_color_trajectory_dialog,
    calculate_admittance_dialog,
    calculate_circle_dialog,
    calculate_electric_field_dialog,
    calculate_reflection_monitoring_dialog,
    calculate_transmission_monitoring_dialog,
    calculate_ellipsometry_monitoring_dialog,
)
from .GUI_plot import rename_dialog, properties_dialog
# Modules are accessed through the main window's modules list
# from ..modules.rugate import rugate_dialog
# from ..modules.quintic import quintic_dialog


class TestCase(object):
    def __init__(self, name, action, requires=None):
        self.name = name
        self.action = action
        self.requires = requires or set()


class menu_selftest_dialog(wx.Dialog):
    def __init__(self, parent):
        wx.Dialog.__init__(self, parent, -1, "Menu self-test", style=wx.CAPTION|wx.RESIZE_BORDER)
        self.parent = parent

        self.log_ctrl = wx.TextCtrl(self, -1, "", style=wx.TE_MULTILINE|wx.TE_READONLY|wx.HSCROLL, size=(700, 400))
        self.run_btn = wx.Button(self, -1, "Run")
        self.save_btn = wx.Button(self, -1, "Save log")
        self.close_btn = wx.Button(self, wx.ID_CLOSE)

        btns = wx.BoxSizer(wx.HORIZONTAL)
        btns.Add(self.run_btn, 0)
        btns.Add(self.save_btn, 0, wx.LEFT, 10)
        btns.Add(self.close_btn, 0, wx.LEFT, 10)

        sizer = wx.BoxSizer(wx.VERTICAL)
        sizer.Add(self.log_ctrl, 1, wx.EXPAND|wx.ALL, 10)
        sizer.Add(btns, 0, wx.ALIGN_RIGHT|wx.ALL, 10)
        self.SetSizerAndFit(sizer)
        self.SetSize((760, 520))

        self.Bind(wx.EVT_BUTTON, self.run_all, self.run_btn)
        self.Bind(wx.EVT_BUTTON, self.save_log, self.save_btn)
        self.Bind(wx.EVT_BUTTON, lambda evt: self.EndModal(wx.ID_OK), self.close_btn)

        self.tests = self._build_tests()

    def log(self, msg):
        ts = time.strftime("%H:%M:%S")
        line = f"[{ts}] {msg}\n"
        self.log_ctrl.AppendText(line)
        try:
            print(line.strip())
        except Exception:
            pass

    def save_log(self, event=None):
        try:
            with open("diagnostics_menu_selftest.log", "w", encoding="utf-8") as f:
                f.write(self.log_ctrl.GetValue())
            self.log("Saved log to diagnostics_menu_selftest.log")
        except Exception as e:
            wx.MessageBox("Failed to save log: %s" % str(e), "Error", wx.ICON_ERROR|wx.OK)

    def _build_tests(self):
        tests = []
        
        # Project → Add Target (discrete)
        tests.append(TestCase("Project/Add Target/Reflection", lambda: self._show_dialog(reflection_target_dialog(self.parent))))
        tests.append(TestCase("Project/Add Target/Transmission", lambda: self._show_dialog(transmission_target_dialog(self.parent))))
        tests.append(TestCase("Project/Add Target/Absorption", lambda: self._show_dialog(absorption_target_dialog(self.parent))))
        
        # Project → Add Target (phase)
        tests.append(TestCase("Project/Add Target/Reflection Phase", lambda: self._show_dialog(reflection_phase_target_dialog(self.parent))))
        tests.append(TestCase("Project/Add Target/Transmission Phase", lambda: self._show_dialog(transmission_phase_target_dialog(self.parent))))
        
        # Project → Add Target (GD)
        tests.append(TestCase("Project/Add Target/Reflection GD", lambda: self._show_dialog(reflection_GD_target_dialog(self.parent))))
        tests.append(TestCase("Project/Add Target/Transmission GD", lambda: self._show_dialog(transmission_GD_target_dialog(self.parent))))
        
        # Project → Add Target (GDD)
        tests.append(TestCase("Project/Add Target/Reflection GDD", lambda: self._show_dialog(reflection_GDD_target_dialog(self.parent))))
        tests.append(TestCase("Project/Add Target/Transmission GDD", lambda: self._show_dialog(transmission_GDD_target_dialog(self.parent))))
        
        # Project → Add Target (color)
        tests.append(TestCase("Project/Add Target/Reflection Color", lambda: self._show_dialog(reflection_color_target_dialog(self.parent))))
        tests.append(TestCase("Project/Add Target/Transmission Color", lambda: self._show_dialog(transmission_color_target_dialog(self.parent))))
        
        # Project → Add Target (spectrum)
        tests.append(TestCase("Project/Add Target/Reflection Spectrum", lambda: self._show_dialog(reflection_spectrum_target_dialog(self.parent))))
        tests.append(TestCase("Project/Add Target/Transmission Spectrum", lambda: self._show_dialog(transmission_spectrum_target_dialog(self.parent))))
        tests.append(TestCase("Project/Add Target/Absorption Spectrum", lambda: self._show_dialog(absorption_spectrum_target_dialog(self.parent))))
        
        # Project → Add Target (phase spectrum)
        tests.append(TestCase("Project/Add Target/Reflection Phase Spectrum", lambda: self._show_dialog(reflection_phase_spectrum_target_dialog(self.parent))))
        tests.append(TestCase("Project/Add Target/Transmission Phase Spectrum", lambda: self._show_dialog(transmission_phase_spectrum_target_dialog(self.parent))))
        
        # Project → Add Target (GD spectrum)
        tests.append(TestCase("Project/Add Target/Reflection GD Spectrum", lambda: self._show_dialog(reflection_GD_spectrum_target_dialog(self.parent))))
        tests.append(TestCase("Project/Add Target/Transmission GD Spectrum", lambda: self._show_dialog(transmission_GD_spectrum_target_dialog(self.parent))))
        
        # Project → Add Target (GDD spectrum)
        tests.append(TestCase("Project/Add Target/Reflection GDD Spectrum", lambda: self._show_dialog(reflection_GDD_spectrum_target_dialog(self.parent))))
        tests.append(TestCase("Project/Add Target/Transmission GDD Spectrum", lambda: self._show_dialog(transmission_GDD_spectrum_target_dialog(self.parent))))
        
        # Filter dialogs (require filter context)
        tests.append(TestCase("Filter/Add layer", self._test_add_layer, requires={"filter"}))
        tests.append(TestCase("Filter/Import layer", self._test_import_layer, requires={"filter"}))
        tests.append(TestCase("Filter/Stack Formula", self._test_stack_formula, requires={"filter"}))
        tests.append(TestCase("Filter/Modules/Rugate", self._test_rugate, requires={"filter"}))
        tests.append(TestCase("Filter/Modules/Quintic", self._test_quintic, requires={"filter"}))
        
        # Design/Optimize (require filter and targets)
        tests.append(TestCase("Design/Refine", self._test_refine, requires={"filter","target"}))
        tests.append(TestCase("Design/Needles", self._test_needles, requires={"filter","target"}))
        tests.append(TestCase("Design/Steps", self._test_steps, requires={"filter","target"}))
        
        # Materials
        tests.append(TestCase("Materials/Manage", self._test_manage_materials))
        tests.append(TestCase("Materials/New Material", self._test_new_material))
        tests.append(TestCase("Materials/Import Material", self._test_import_material))
        
        # Preproduction
        tests.append(TestCase("Preproduction/Simulate random errors", self._test_random_errors, requires={"filter","target"}))
        
        # Calculate (spectrum/phase/GD/GDD/color/diagrams/monitoring)
        tests.append(TestCase("Calculate/Reflection", self._test_calc_reflection, requires={"filter"}))
        tests.append(TestCase("Calculate/Transmission", self._test_calc_transmission, requires={"filter"}))
        tests.append(TestCase("Calculate/Absorption", self._test_calc_absorption, requires={"filter"}))
        tests.append(TestCase("Calculate/Reflection phase", self._test_calc_reflection_phase, requires={"filter"}))
        tests.append(TestCase("Calculate/Transmission phase", self._test_calc_transmission_phase, requires={"filter"}))
        tests.append(TestCase("Calculate/Reflection GD", self._test_calc_reflection_gd, requires={"filter"}))
        tests.append(TestCase("Calculate/Transmission GD", self._test_calc_transmission_gd, requires={"filter"}))
        tests.append(TestCase("Calculate/Reflection GDD", self._test_calc_reflection_gdd, requires={"filter"}))
        tests.append(TestCase("Calculate/Transmission GDD", self._test_calc_transmission_gdd, requires={"filter"}))
        tests.append(TestCase("Calculate/Color", self._test_calc_color, requires={"filter"}))
        tests.append(TestCase("Calculate/Color trajectory", self._test_calc_color_traj, requires={"filter"}))
        tests.append(TestCase("Calculate/Admittance", self._test_calc_admittance, requires={"filter"}))
        tests.append(TestCase("Calculate/Circle", self._test_calc_circle, requires={"filter"}))
        tests.append(TestCase("Calculate/Electric field", self._test_calc_electric_field, requires={"filter"}))
        tests.append(TestCase("Calculate/Reflection monitoring", self._test_calc_reflection_monitoring, requires={"filter"}))
        tests.append(TestCase("Calculate/Transmission monitoring", self._test_calc_transmission_monitoring, requires={"filter"}))
        tests.append(TestCase("Calculate/Ellipsometry monitoring", self._test_calc_ellipsometry_monitoring, requires={"filter"}))
        
        # Plot
        tests.append(TestCase("Plot/Rename", self._test_plot_rename))
        tests.append(TestCase("Plot/Properties", self._test_plot_properties))
        
        return tests

    def run_all(self, event=None):
        self.log_ctrl.SetValue("")
        self.log("Starting menu self-test")
        self._prepare_context()
        passed = failed = 0
        for case in self.tests:
            try:
                self._ensure_requirements(case.requires)
                case.action()
                self.log(f"{case.name}: PASS")
                passed += 1
            except Exception as e:
                self.log(f"{case.name}: FAIL - {e}")
                failed += 1
        self.log(f"Completed. Passed={passed}, Failed={failed}")

    def _prepare_context(self):
        # Ensure project exists
        if not hasattr(self.parent, "project") or self.parent.project is None:
            try:
                self.parent.on_new_project(None)
            except Exception:
                pass

    def _ensure_requirements(self, reqs):
        reqs = reqs or set()
        if "filter" in reqs:
            if getattr(self.parent, "selected_filter_nb", -1) < 0:
                # Try to create a minimal filter through existing GUI method
                try:
                    self.parent.on_new_filter()
                except Exception:
                    # Fallback: show properties dialog which may create a default filter
                    self.parent.on_filter_properties()
        if "target" in reqs:
            if getattr(self.parent, "selected_target_nb", -1) < 0:
                # Open reflection target dialog and cancel immediately after set defaults
                self._show_dialog(reflection_target_dialog(self.parent))

    def _show_dialog(self, dlg):
        wx.CallLater(120, dlg.EndModal, wx.ID_CANCEL)
        dlg.ShowModal()
        dlg.Destroy()

    # Specific tests using existing dialogs
    def _test_add_layer(self):
        dlg = simple_layer_dialog(self.parent, self.parent.project.get_filter(self.parent.selected_filter_nb))
        self._show_dialog(dlg)

    def _test_import_layer(self):
        dlg = import_layer_dialog(self.parent, self.parent.project.get_filter(self.parent.selected_filter_nb))
        self._show_dialog(dlg)

    def _test_stack_formula(self):
        dlg = stack_dialog(self.parent, self.parent.project.get_filter(self.parent.selected_filter_nb))
        self._show_dialog(dlg)

    def _test_rugate(self):
        # Find the rugate module and execute its dialog
        for module in self.parent.modules:
            if module.get_name() == "Rugate":
                for submodule in module.get_submodule():
                    if "nb periods and amplitude" in submodule.get_name():
                        dlg = submodule.execute_dialog(self.parent, self.parent.project.get_filter(self.parent.selected_filter_nb))
                        self._show_dialog(dlg)
                        return
        self.log("ERROR: Could not find Rugate module")

    def _test_quintic(self):
        # Find the quintic module and execute its dialog
        for module in self.parent.modules:
            if module.get_name() == "Quintic":
                for submodule in module.get_submodule():
                    if submodule.get_name() == "Quintic":
                        dlg = submodule.execute_dialog(self.parent, self.parent.project.get_filter(self.parent.selected_filter_nb))
                        self._show_dialog(dlg)
                        return
        self.log("ERROR: Could not find Quintic module")

    def _test_refine(self):
        flt = self.parent.project.get_filter(self.parent.selected_filter_nb)
        tgts = self.parent.project.get_targets()
        dlg = optimization_refinement_dialog(self.parent, flt, tgts)
        self._show_dialog(dlg)

    def _test_needles(self):
        flt = self.parent.project.get_filter(self.parent.selected_filter_nb)
        tgts = self.parent.project.get_targets()
        dlg = optimization_needles_dialog(self.parent, flt, tgts)
        self._show_dialog(dlg)

    def _test_steps(self):
        flt = self.parent.project.get_filter(self.parent.selected_filter_nb)
        tgts = self.parent.project.get_targets()
        dlg = optimization_steps_dialog(self.parent, flt, tgts)
        self._show_dialog(dlg)

    def _test_manage_materials(self):
        dlg = manage_materials_dialog(self.parent, self.parent.get_material_catalog(), True)
        self._show_dialog(dlg)

    def _test_new_material(self):
        dlg = new_material_dialog(self.parent)
        self._show_dialog(dlg)

    def _test_import_material(self):
        dlg = import_material_dialog(self.parent)
        self._show_dialog(dlg)

    def _test_random_errors(self):
        flt = self.parent.project.get_filter(self.parent.selected_filter_nb)
        tgts = self.parent.project.get_targets()
        dlg = random_errors_dialog(self.parent, flt, tgts)
        self._show_dialog(dlg)

    # Calculate dialogs
    def _test_calc_reflection(self):
        dlg = calculate_reflection_dialog(self.parent, self.parent.project.get_filter(self.parent.selected_filter_nb))
        self._show_dialog(dlg)

    def _test_calc_transmission(self):
        dlg = calculate_transmission_dialog(self.parent, self.parent.project.get_filter(self.parent.selected_filter_nb))
        self._show_dialog(dlg)

    def _test_calc_absorption(self):
        dlg = calculate_absorption_dialog(self.parent, self.parent.project.get_filter(self.parent.selected_filter_nb))
        self._show_dialog(dlg)

    def _test_calc_reflection_phase(self):
        dlg = calculate_reflection_phase_dialog(self.parent, self.parent.project.get_filter(self.parent.selected_filter_nb))
        self._show_dialog(dlg)

    def _test_calc_transmission_phase(self):
        dlg = calculate_transmission_phase_dialog(self.parent, self.parent.project.get_filter(self.parent.selected_filter_nb))
        self._show_dialog(dlg)

    def _test_calc_reflection_gd(self):
        dlg = calculate_reflection_GD_dialog(self.parent, self.parent.project.get_filter(self.parent.selected_filter_nb))
        self._show_dialog(dlg)

    def _test_calc_transmission_gd(self):
        dlg = calculate_transmission_GD_dialog(self.parent, self.parent.project.get_filter(self.parent.selected_filter_nb))
        self._show_dialog(dlg)

    def _test_calc_reflection_gdd(self):
        dlg = calculate_reflection_GDD_dialog(self.parent, self.parent.project.get_filter(self.parent.selected_filter_nb))
        self._show_dialog(dlg)

    def _test_calc_transmission_gdd(self):
        dlg = calculate_transmission_GDD_dialog(self.parent, self.parent.project.get_filter(self.parent.selected_filter_nb))
        self._show_dialog(dlg)

    def _test_calc_color(self):
        dlg = calculate_color_dialog(self.parent, self.parent.project.get_filter(self.parent.selected_filter_nb))
        self._show_dialog(dlg)

    def _test_calc_color_traj(self):
        dlg = calculate_color_trajectory_dialog(self.parent, self.parent.project.get_filter(self.parent.selected_filter_nb))
        self._show_dialog(dlg)

    def _test_calc_admittance(self):
        dlg = calculate_admittance_dialog(self.parent, self.parent.project.get_filter(self.parent.selected_filter_nb))
        self._show_dialog(dlg)

    def _test_calc_circle(self):
        dlg = calculate_circle_dialog(self.parent, self.parent.project.get_filter(self.parent.selected_filter_nb))
        self._show_dialog(dlg)

    def _test_calc_electric_field(self):
        dlg = calculate_electric_field_dialog(self.parent, self.parent.project.get_filter(self.parent.selected_filter_nb))
        self._show_dialog(dlg)

    def _test_calc_reflection_monitoring(self):
        dlg = calculate_reflection_monitoring_dialog(self.parent, self.parent.project.get_filter(self.parent.selected_filter_nb))
        self._show_dialog(dlg)

    def _test_calc_transmission_monitoring(self):
        dlg = calculate_transmission_monitoring_dialog(self.parent, self.parent.project.get_filter(self.parent.selected_filter_nb))
        self._show_dialog(dlg)

    def _test_calc_ellipsometry_monitoring(self):
        dlg = calculate_ellipsometry_monitoring_dialog(self.parent, self.parent.project.get_filter(self.parent.selected_filter_nb))
        self._show_dialog(dlg)

    # Plot dialogs
    def _test_plot_rename(self):
        dlg = rename_dialog(self.parent)
        self._show_dialog(dlg)

    def _test_plot_properties(self):
        # Use an existing plot object that has the required methods
        if hasattr(self.parent, 'photometry_plot') and self.parent.photometry_plot:
            dlg = properties_dialog(self.parent.photometry_plot)
            self._show_dialog(dlg)
        else:
            self.log("ERROR: No plot object available for properties test")


