# OpenFilters 使用指南

## Filter菜单功能说明

Filter菜单包含了滤光片设计和编辑的核心功能。在使用这些功能之前，请确保：

### 前置条件
1. **创建新项目**：使用 `File` → `New Project`
2. **添加滤光片**：使用 `Project` → `Add Filter`
3. **选中滤光片**：在滤光片列表中选中一个滤光片

### Filter菜单功能

一旦满足前置条件，Filter菜单的以下功能将变为可用：

#### 基本功能
- **Properties** (Alt+Enter)：查看和修改滤光片的基本属性
- **Add layer**：向滤光片添加新的薄膜层
- **Remove layer**：删除选中的薄膜层

#### 高级功能
- **Stack Formula**：使用公式定义薄膜堆栈结构
- **Import layer**：从其他来源导入薄膜层数据
- **Merge layers**：合并多个薄膜层
- **Convert mixture to steps**：将混合材料转换为阶梯结构
- **Swap sides**：交换前后表面的薄膜层

#### 模块功能
- **Modules**：访问专业模块（如Rugate等）

#### 导出功能
- **Export front index profile**：导出前表面折射率分布
- **Export back index profile**：导出后表面折射率分布

## 故障排除

### Filter菜单灰色不可用
**问题**：Filter菜单中的选项都是灰色，无法点击

**解决方案**：
1. 确保已创建项目：`File` → `New Project`
2. 添加至少一个滤光片：`Project` → `Add Filter`
3. 在滤光片列表中选中一个滤光片
4. Filter菜单功能将自动变为可用

### Properties对话框无法打开
**问题**：点击Properties菜单项时没有响应

**解决方案**：
1. 检查是否选中了滤光片（selected_filter_nb >= 0）
2. 确保项目中有滤光片数据
3. 重新选择滤光片

## 技术修复说明

本次修复解决了以下问题：

1. **wxPython兼容性**：
   - 将所有 `wx.PyValidator` 替换为 `wx.Validator`
   - 消除了wxPython 4.x的弃用警告

2. **菜单状态管理**：
   - Filter菜单功能需要先创建项目和添加滤光片
   - 菜单状态通过 `update_menu()` 方法动态更新

3. **事件绑定**：
   - 所有Filter菜单项都正确绑定到相应的处理方法
   - Properties功能绑定到 `on_filter_properties` 方法

现在所有Filter菜单功能都可以正常工作！