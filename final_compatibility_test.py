#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Final compatibility test for OpenFilters Python 3.11 migration
"""

import sys
import subprocess
import time

def run_test(test_name, command, cwd=None):
    """Run a test and return the result"""
    print(f"\n{'='*60}")
    print(f"Running: {test_name}")
    print(f"Command: {command}")
    print(f"{'='*60}")
    
    start_time = time.perf_counter()
    
    try:
        result = subprocess.run(
            command,
            shell=True,
            capture_output=True,
            text=True,
            cwd=cwd,
            timeout=60
        )
        
        end_time = time.perf_counter()
        duration = end_time - start_time
        
        if result.returncode == 0:
            print(f"✅ PASSED ({duration:.2f}s)")
            if result.stdout.strip():
                print("Output:")
                print(result.stdout[:500] + ("..." if len(result.stdout) > 500 else ""))
            return True
        else:
            print(f"❌ FAILED ({duration:.2f}s)")
            print("Error output:")
            print(result.stderr[:500] + ("..." if len(result.stderr) > 500 else ""))
            return False
            
    except subprocess.TimeoutExpired:
        print("❌ TIMEOUT (60s)")
        return False
    except Exception as e:
        print(f"❌ EXCEPTION: {e}")
        return False

def main():
    """Run all compatibility tests"""
    print("🔬 OpenFilters Python 3.11 Compatibility Test Suite")
    print(f"Python version: {sys.version}")
    print(f"Test started at: {time.strftime('%Y-%m-%d %H:%M:%S')}")
    
    tests = [
        ("Basic Import Test", "python -c \"import abeles, moremath, color, materials; print('All modules imported successfully')\""),
        ("Main Tests", "python tests.py"),
        ("Abeles Tests", "cd abeles && python tests.py"),
        ("Compatibility Test", "python test_python3_compatibility.py"),
        ("Main Program Launch", "python -c \"import Filters; print('Main program can be imported')\""),
    ]
    
    passed = 0
    failed = 0
    
    for test_name, command in tests:
        if run_test(test_name, command):
            passed += 1
        else:
            failed += 1
    
    print(f"\n{'='*60}")
    print("📊 FINAL RESULTS")
    print(f"{'='*60}")
    print(f"✅ Passed: {passed}")
    print(f"❌ Failed: {failed}")
    print(f"📈 Success Rate: {passed/(passed+failed)*100:.1f}%")
    
    if failed == 0:
        print("\n🎉 ALL TESTS PASSED!")
        print("OpenFilters has been successfully migrated to Python 3.11!")
        print("\n✨ Summary of fixes applied:")
        print("  • Fixed all print statements (Python 2 → Python 3 syntax)")
        print("  • Replaced time.clock() with time.perf_counter()")
        print("  • Updated C++ extension Makefiles for Python 3.11")
        print("  • Fixed observer/illuminant name case sensitivity")
        print("  • Verified all core modules work correctly")
        return 0
    else:
        print(f"\n⚠️  {failed} test(s) failed. Please review the errors above.")
        return 1

if __name__ == "__main__":
    sys.exit(main())
