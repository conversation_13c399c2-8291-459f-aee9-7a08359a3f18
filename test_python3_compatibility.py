#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Test script to verify Python 3 compatibility fixes for OpenFilters
"""

import sys
import time

def test_print_statements():
    """Test that print statements work correctly"""
    print("Testing print statements...")
    print("Print with string:", "Hello World")
    print("Print with number:", 42)
    print("Print with multiple args:", 1, 2, 3)
    print("")  # Empty print
    return True

def test_time_perf_counter():
    """Test that time.perf_counter() works correctly"""
    print("Testing time.perf_counter()...")
    start = time.perf_counter()
    time.sleep(0.01)  # Sleep for 10ms
    stop = time.perf_counter()
    elapsed = stop - start
    print(f"Elapsed time: {elapsed:.6f} seconds")
    assert elapsed > 0.005, "Timer should measure at least 5ms"
    return True

def test_imports():
    """Test that all main modules can be imported"""
    print("Testing module imports...")
    
    try:
        import abeles
        print("✓ abeles module imported successfully")
    except ImportError as e:
        print(f"✗ Failed to import abeles: {e}")
        return False
    
    try:
        import moremath
        print("✓ moremath module imported successfully")
    except ImportError as e:
        print(f"✗ Failed to import moremath: {e}")
        return False
    
    try:
        import color
        print("✓ color module imported successfully")
    except ImportError as e:
        print(f"✗ Failed to import color: {e}")
        return False
    
    try:
        import materials
        print("✓ materials module imported successfully")
    except ImportError as e:
        print(f"✗ Failed to import materials: {e}")
        return False
    
    return True

def test_string_operations():
    """Test string operations work correctly"""
    print("Testing string operations...")
    
    # Test string formatting
    name = "OpenFilters"
    version = "1.1.1"
    formatted = f"{name} version {version}"
    print(f"String formatting: {formatted}")
    
    # Test old-style formatting (still used in the code)
    old_style = "%s version %s" % (name, version)
    print(f"Old-style formatting: {old_style}")
    
    return True

def test_division():
    """Test division operations"""
    print("Testing division operations...")
    
    # Test true division
    result1 = 5 / 2
    print(f"5 / 2 = {result1}")
    assert result1 == 2.5, "True division should return float"
    
    # Test floor division
    result2 = 5 // 2
    print(f"5 // 2 = {result2}")
    assert result2 == 2, "Floor division should return integer"
    
    return True

def main():
    """Run all tests"""
    print("=" * 50)
    print("OpenFilters Python 3 Compatibility Test")
    print("=" * 50)
    print(f"Python version: {sys.version}")
    print("")
    
    tests = [
        test_print_statements,
        test_time_perf_counter,
        test_imports,
        test_string_operations,
        test_division,
    ]
    
    passed = 0
    failed = 0
    
    for test in tests:
        try:
            if test():
                print(f"[PASS] {test.__name__} PASSED")
                passed += 1
            else:
                print(f"[FAIL] {test.__name__} FAILED")
                failed += 1
        except Exception as e:
            print(f"[FAIL] {test.__name__} FAILED with exception: {e}")
            failed += 1
        print("")
    
    print("=" * 50)
    print(f"Test Results: {passed} passed, {failed} failed")
    print("=" * 50)
    
    if failed == 0:
        print("[SUCCESS] All tests passed! OpenFilters is compatible with Python 3.11")
        return 0
    else:
        print("[ERROR] Some tests failed. Please check the issues above.")
        return 1

if __name__ == "__main__":
    sys.exit(main())
